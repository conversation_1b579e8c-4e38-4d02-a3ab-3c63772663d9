export interface LLMProvider {
  id: string;
  name: string;
  costPerMinute: number; // in USD
  category: 'gemini' | 'claude' | 'gpt' | 'other';
}

export const LLM_PROVIDERS: LLMProvider[] = [
  // Gemini Models
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    costPerMinute: 0.0011,
    category: 'gemini'
  },
  {
    id: 'gemini-2.0-flash',
    name: 'Gemini 2.0 Flash',
    costPerMinute: 0.0007,
    category: 'gemini'
  },
  {
    id: 'gemini-2.0-flash-lite',
    name: 'Gemini 2.0 Flash Lite',
    costPerMinute: 0.0005,
    category: 'gemini'
  },
  
  // Claude Models
  {
    id: 'claude-sonnet-4',
    name: '<PERSON> Sonnet 4',
    costPerMinute: 0.0224,
    category: 'claude'
  },
  {
    id: 'claude-3.7-sonnet',
    name: 'Claude 3.7 Sonnet',
    costPerMinute: 0.0224,
    category: 'claude'
  },
  {
    id: 'claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    costPerMinute: 0.0224,
    category: 'claude'
  },
  {
    id: 'claude-3-haiku',
    name: '<PERSON> 3 <PERSON><PERSON>',
    costPerMinute: 0.0019,
    category: 'claude'
  },
  
  // GPT Models
  {
    id: 'gpt-4.1',
    name: 'GPT-4.1',
    costPerMinute: 0.0143,
    category: 'gpt'
  },
  {
    id: 'gpt-4.1-mini',
    name: 'GPT-4.1 Mini',
    costPerMinute: 0.0029,
    category: 'gpt'
  },
  {
    id: 'gpt-4.1-nano',
    name: 'GPT-4.1 Nano',
    costPerMinute: 0.0007,
    category: 'gpt'
  },
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    costPerMinute: 0.0179,
    category: 'gpt'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    costPerMinute: 0.0011,
    category: 'gpt'
  },
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    costPerMinute: 0.0687,
    category: 'gpt'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    costPerMinute: 0.0034,
    category: 'gpt'
  }
];

// Helper function to format cost
export function formatCost(costPerMinute: number): string {
  return `~$${costPerMinute.toFixed(4)}`;
}

// Helper function to get provider by ID
export function getLLMProvider(id: string): LLMProvider | undefined {
  return LLM_PROVIDERS.find(provider => provider.id === id);
}

// Helper function to get providers by category
export function getLLMProvidersByCategory(category: LLMProvider['category']): LLMProvider[] {
  return LLM_PROVIDERS.filter(provider => provider.category === category);
}
