'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'

// Define the widget settings type
export interface WidgetSettings {
  position: string
  customText: {
    needHelp: string
    talkToInterrupt: string
    listening: string
    startCall: string
    endCall: string
    noMessages: string
    aiLabel: string
    userLabel: string
    chatHistory: string
  }
  hideIcon: boolean
  customIcon: string
  customCSS: string
  widgetType?: 'standalone' | 'compact'
}

// Default widget settings
export const defaultWidgetSettings: WidgetSettings = {
  position: 'bottom-right',
  customText: {
    needHelp: 'Want to chat?',
    talkToInterrupt: 'Speak now to interrupt',
    listening: "I'm listening...",
    startCall: 'Start conversation',
    endCall: 'Finish chat',
    noMessages: 'Start your conversation',
    aiLabel: 'Assistant',
    userLabel: 'Me',
    chatHistory: 'Conversation History'
  },
  hideIcon: false,
  customIcon: '',
  customCSS: '',
  widgetType: 'standalone'
}

// Define the context type
interface WidgetContextType {
  widgetSettings: WidgetSettings
  updateWidgetSettings: (settings: Partial<WidgetSettings>) => void
  updateCustomText: (key: keyof WidgetSettings['customText'], value: string) => void
  isDirty: boolean
  setIsDirty: (dirty: boolean) => void
}

// Create the context with a default value
const WidgetContext = createContext<WidgetContextType | undefined>(undefined)

// Provider component
export function WidgetProvider({
  children,
  initialSettings = defaultWidgetSettings
}: {
  children: ReactNode
  initialSettings?: WidgetSettings
}) {
  const [widgetSettings, setWidgetSettings] = useState<WidgetSettings>(initialSettings)
  const [isDirty, setIsDirty] = useState(false)

  const updateWidgetSettings = (settings: Partial<WidgetSettings>) => {
    setWidgetSettings(prev => ({ ...prev, ...settings }))
    setIsDirty(true)

    // Dispatch an event to notify components that settings have changed
    if (typeof window !== 'undefined') {
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('widget-settings-changed', {
          detail: { timestamp: Date.now() }
        }));
      }, 0);
    }
  }

  const updateCustomText = (key: keyof WidgetSettings['customText'], value: string) => {
    setWidgetSettings(prev => ({
      ...prev,
      customText: {
        ...prev.customText,
        [key]: value
      }
    }))
    setIsDirty(true)

    // Dispatch an event to notify components that settings have changed
    if (typeof window !== 'undefined') {
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('widget-settings-changed', {
          detail: { timestamp: Date.now() }
        }));
      }, 0);
    }
  }

  return (
    <WidgetContext.Provider
      value={{
        widgetSettings,
        updateWidgetSettings,
        updateCustomText,
        isDirty,
        setIsDirty
      }}
      data-widget-provider="true"
    >
      {children}
    </WidgetContext.Provider>
  )
}

// Custom hook to use the widget context
export function useWidgetContext() {
  const context = useContext(WidgetContext)
  if (context === undefined) {
    throw new Error('useWidgetContext must be used within a WidgetProvider')
  }
  return context
}
