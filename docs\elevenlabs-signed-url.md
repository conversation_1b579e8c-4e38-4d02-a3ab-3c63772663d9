# ElevenLabs Signed URL API

This document describes how to use the ElevenLabs Signed URL API endpoint.

## Overview

The Signed URL API endpoint allows you to obtain a signed URL for an ElevenLabs agent, which can be used to establish a direct connection to the ElevenLabs service. Before providing the URL, the endpoint checks if the agent has sufficient budget (credits) available.

The endpoint retrieves the ElevenLabs agent ID from the `external_provider_id` field in the `agent_configs` table.

## Endpoint

```
GET /api/elevenlabs/signed-url
```

## Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| agentId | string | Yes | The ID of the agent in your database |
| estimatedCost | number | No | The estimated cost of the call in cents (default: 10) |

## Response

### Success Response

```json
{
  "url": "https://api.elevenlabs.io/v1/signed-url/...",
  "allow_call": true,
  "agent_id": "elevenlabs-agent-id",
  "success": true
}
```

### Error Responses

#### Missing Agent ID

```json
{
  "error": "Missing agentId parameter",
  "status": 400
}
```

#### Agent Not Found

```json
{
  "error": "Agent not found",
  "status": 404
}
```

#### ElevenLabs Agent ID Not Found

```json
{
  "error": "ElevenLabs agent ID not found for this agent",
  "status": 404
}
```

#### Insufficient Budget

```json
{
  "error": "Insufficient budget",
  "allow_call": false,
  "details": "There are not enough credits available to start this call.",
  "status": 403
}
```

The endpoint may provide more specific budget error messages:

- "Insufficient organization credits"
- "Agent daily budget limit reached"
- "Team budget limit reached"

#### Server Error

```json
{
  "error": "An unexpected error occurred",
  "details": "Error message",
  "success": false,
  "status": 500
}
```

## Usage Example

### JavaScript/TypeScript

```javascript
async function getSignedUrl(agentId) {
  try {
    const response = await fetch(`/api/elevenlabs/signed-url?agentId=${agentId}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to get signed URL');
    }

    return data.url;
  } catch (error) {
    console.error('Error getting signed URL:', error);
    throw error;
  }
}
```

### Widget Integration

To use the signed URL with the widget:

```javascript
// Initialize the widget with the signed URL
async function initializeWidget(agentId, containerId) {
  try {
    // Get the signed URL
    const response = await fetch(`/api/elevenlabs/signed-url?agentId=${agentId}`);
    const data = await response.json();

    if (!response.ok) {
      console.error('Error getting signed URL:', data.error);
      // Show error message to the user
      return;
    }

    // Initialize the widget with the signed URL
    ConversationWidget({
      signedUrl: data.url,
      containerId: containerId,
      // Other widget options...
    });
  } catch (error) {
    console.error('Error initializing widget:', error);
  }
}
```

## Budget Checking

The endpoint performs several budget checks before returning a signed URL:

1. **Organization Budget**: Checks if the organization has enough credits in its wallet
2. **Agent Daily Limit**: Checks if the agent has reached its daily budget limit
3. **Team Budget Limit**: Checks if the team the agent belongs to has reached its budget limit

If any of these checks fail, the endpoint will return an error response with details about why the call cannot be started.

## Credit Deduction

This endpoint does not deduct credits. Credits are deducted after the call is completed via the ElevenLabs post-call webhook.
