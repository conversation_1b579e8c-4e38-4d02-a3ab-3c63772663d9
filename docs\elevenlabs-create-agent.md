# ElevenLabs Create Agent API

This document describes how to use the ElevenLabs Create Agent API endpoint.

## Overview

The Create Agent API endpoint creates an agent in ElevenLabs and stores its ID in the `external_provider_id` field of the `agent_configs` table. This ensures that the agent is properly linked to ElevenLabs and can be used with the Signed URL API.

## Endpoint

```
POST /api/elevenlabs/create-agent
```

## Request Body

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | Yes | The name of the agent |
| orgId | string | Yes | The ID of the organization |
| teamId | string | No | The ID of the team (if not provided, the agent won't be assigned to a team) |
| budget | number | No | The budget for the agent in dollars (will be converted to cents) |
| voiceId | string | No | The ID of the ElevenLabs voice to use (defaults to "eleven_flash_v2") |
| description | string | No | A description of the agent |
| conversation_config | object | No | Configuration for the ElevenLabs agent (see below) |

### Conversation Config

The `conversation_config` object can include various settings for the ElevenLabs agent. Here's a basic example:

```json
{
  "agent": {
    "prompt": {
      "prompt": "You are a helpful assistant.",
      "llm": "gemini-2.0-flash-001",
      "tools": [],
      "knowledge_base": [],
      "temperature": 0.5,
      "max_tokens": -1
    }
  },
  "tts": {
    "model_id": "eleven_flash_v2",
    "agent_output_audio_format": "pcm_16000",
    "optimize_streaming_latency": 3,
    "stability": 0.5,
    "similarity_boost": 0.8
  }
}
```

## Response

### Success Response

```json
{
  "success": true,
  "agent": {
    "id": "agent-id",
    "name": "Agent Name",
    "elevenlabs_agent_id": "elevenlabs-agent-id"
  }
}
```

### Error Responses

#### Missing Required Parameters

```json
{
  "error": "Missing required parameters: name and orgId are required",
  "status": 400
}
```

#### Failed to Create ElevenLabs Agent

```json
{
  "error": "Failed to create ElevenLabs agent",
  "details": "Error message",
  "status": 500
}
```

#### Failed to Create Agent in Database

```json
{
  "error": "Failed to create agent in database",
  "details": "Error message",
  "status": 500
}
```

#### Failed to Create Agent Config

```json
{
  "error": "Failed to create agent config",
  "details": "Error message",
  "status": 500
}
```

#### Server Error

```json
{
  "error": "An unexpected error occurred",
  "details": "Error message",
  "status": 500
}
```

## Usage Example

### JavaScript/TypeScript

```javascript
async function createAgent(name, orgId, teamId, budget) {
  try {
    const response = await fetch('/api/elevenlabs/create-agent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        orgId,
        teamId,
        budget,
        voiceId: 'eleven_flash_v2',
        conversation_config: {
          agent: {
            prompt: {
              prompt: 'You are a helpful assistant.',
              llm: 'gemini-2.0-flash-001',
              temperature: 0.5
            }
          }
        }
      }),
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || 'Failed to create agent');
    }
    
    return data.agent;
  } catch (error) {
    console.error('Error creating agent:', error);
    throw error;
  }
}
```

## Notes

- The endpoint creates both the ElevenLabs agent and the database records in a single transaction.
- If any part of the process fails, the endpoint will attempt to clean up by deleting any resources that were created.
- The ElevenLabs agent ID is stored in the `external_provider_id` field of the `agent_configs` table.
- The agent name in ElevenLabs will be formatted as `{name} [SB-{orgId}]` to make it easier to identify.
