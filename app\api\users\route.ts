import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET /api/users
 * Get users with optional filtering
 */
export async function GET(request: Request) {
  try {
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const unassigned = searchParams.get('unassigned') === 'true';
    const tenantId = searchParams.get('tenantId');

    // Calculate pagination
    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;

    // Build query
    let query = supabase
      .from('users')
      .select('*', { count: 'exact' });

    // Add filters
    if (unassigned) {
      // Get users who don't belong to any tenant
      query = query.not(
        'id', 'in',
        supabase
          .from('tenant_users')
          .select('user_id')
      );
    } else if (tenantId) {
      // Get users who belong to a specific tenant
     // First, fetch the list of user_ids for the tenant
        const { data: tenantUsers, error } = await supabase
        .from('tenant_users')
        .select('user_id')
        .eq('tenant_id', tenantId);

        if (error) {
        throw new Error('Error fetching tenant users: ' + error.message);
        }

        // Extract the user_ids into an array
        const userIds = tenantUsers.map(user => user.user_id);

        // Now apply the .in() filter with that list
        query = query.in('id', userIds.filter(Boolean) as string[]);

    }

    // Add search filter if provided
    if (search) {
      query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%`);
    }

    // Add pagination
    query = query.range(start, end);

    // Execute query
    const { data, count, error } = await query;

    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    // Calculate if there are more pages
    const hasMore = count ? start + pageSize < count : false;

    return NextResponse.json({
      data,
      count: count || 0,
      hasMore,
      page
    });
  } catch (error) {
    console.error('Error in users route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}





