'use client'

import { useState, useActionState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>et<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useRouter } from 'next/navigation'
import { Card, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { cn } from '@/utils/cn'
import { Check } from 'lucide-react'
import {createAgent} from '@/actions/serverActions'
import { getTemplates } from '@/utils/supabase/queries'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface AddAgentPanelProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  orgId: string
}

const AGENT_TEMPLATES = getTemplates();
const initialState = { success: false, error: '', id: '' };

export function AddAgentPanel({ open, onOpenChange, orgId }: AddAgentPanelProps) {
  const router = useRouter()
  const [state, formAction, isPending] = useActionState(createAgent, initialState)
  const [teams, setTeams] = useState<{id: string, name: string}[]>([])
  // Loading state for teams
  const [isLoading, setIsLoading] = useState(false)

  // Fetch teams when the panel opens
  useEffect(() => {
    if (open) {
      fetchTeams()
    }
  }, [open, orgId])

  // Fetch teams from the API
  const fetchTeams = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/organizations/${orgId}/teams`)
      if (response.ok) {
        const data = await response.json()
        setTeams(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching teams:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle successful agent creation with useEffect instead of during render
  useEffect(() => {
    if (state.success && state.id) {
      router.push(`/dashboard/${orgId}/agents/${state.id}`)
      onOpenChange(false)
    }
  }, [state.success, state.id, router, orgId, onOpenChange])

  const handleSubmit = async (formData: FormData) => {
    formData.append('orgId', orgId)
    return formAction(formData)
  }


  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
    <SheetContent className="grid grid-rows-[auto_1fr_auto] w-[min(500px,100%)] min-w-[33vw] sm:w-[540px] text-zinc-900 dark:text-zinc-200">
      
      {/* HEADER */}
      <SheetHeader>
        <SheetTitle>Create New Agent</SheetTitle>
      </SheetHeader>
  
      {/* FORM BODY */}
      <form action={handleSubmit} className="overflow-y-auto px-1 sm:px-2 space-y-6 pt-6">
        {/* Agent Name */}
        <div className="space-y-2">
          <label htmlFor="name" className="text-sm font-medium">Agent Name</label>
          <Input id="name" name="name" placeholder="Enter agent name" required />
        </div>
  
        {/* Team Select */}
        <div className="space-y-2">
          <label htmlFor="teamId" className="text-sm font-medium">Team (Optional)</label>
          <Select name="teamId">
            <SelectTrigger className="mt-2" disabled={isLoading}>
              <SelectValue placeholder={isLoading ? "Loading teams..." : "Select a team"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No team</SelectItem>
              {teams.map((team) => (
                <SelectItem key={team.id} value={team.id}>{team.name}</SelectItem>
              ))}
              {teams.length === 0 && !isLoading && (
                <SelectItem value="no-teams-available" disabled>No teams available</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
  
        {/* Budget */}
        <div className="space-y-1">
          <label htmlFor="budget" className="text-sm font-medium">Budget (USD)</label>
          <div className="relative">
            <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
            <Input id="budget" name="budget" type="number" min="0" step="1" defaultValue="20" className="pl-7" />
          </div>
          <p className="text-xs text-muted-foreground mt-1">Set the maximum budget for this agent. This will be used to track spending.</p>
        </div>
  
        {/* Templates */}
        <div className="flex flex-col space-y-3">
          <label className="text-sm font-medium">Select Template</label>
          <div className="grid grid-cols-1 gap-4">
            {AGENT_TEMPLATES.map((template) => (
              <label key={template.id} className="block cursor-pointer">

                <input type="radio" name="template" value={template.id} className="peer sr-only" required />
                
                <Card className={cn("transition-all border hover:border-primary/50 hover:shadow-sm", "peer-checked:border-primary peer-checked:ring-2 peer-checked:ring-primary")}>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{template.icon}</span>
                      <div className="flex-grow">
                        <CardTitle className="text-base">{template.title}</CardTitle>
                        <CardDescription className="text-sm">{template.description}</CardDescription>
                      </div>
                      {/* <div className={cn("rounded-full border-2 p-1 transition-all", "border-muted-foreground/30 peer-checked:border-primary peer-checked:bg-primary peer-checked:text-white")}>
                        <Check className={cn("h-4 w-4 opacity-0 transition-opacity duration-200", "peer-checked:opacity-100")} />
                      </div> */}
                    </div>
                  </CardHeader>
                </Card>
              </label>
            ))}
          </div>
        </div>
  
        {state.error && <p className="text-red-500">{state.error}</p>}
     
  
      {/* FOOTER (inside the same grid layout) */}
      <SheetFooter className="border-t bg-background py-4 px-2 sm:px-4">
        <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
        <Button type="submit" disabled={isPending}>
          {isPending ? 'Creating...' : 'Create Agent'}
        </Button>
      </SheetFooter>
      </form>
    </SheetContent>
  </Sheet>
  
  )
}