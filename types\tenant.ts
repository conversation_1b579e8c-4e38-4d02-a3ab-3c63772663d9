import { J<PERSON> } from '@/types_db';

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  domain: string;
  created_at?: string;
  updated_at?: string;
  settings?: Json;
  status: 'active' | 'inactive' | 'deleted';
}

export interface TenantAdmin {
  id: string;
  tenant_id: string;
  user_id: string;
  created_at?: string;
}

export interface TenantContext {
  tenant: Tenant | null;
  isAdmin: boolean;
}

export interface TenantSettings {
  theme?: {
    primaryColor?: string;
    logo?: string;
    favicon?: string;
  };
  features?: {
    teams?: boolean;
    knowledgeBase?: boolean;
    analytics?: boolean;
  };
  customDomain?: string;
  contactEmail?: string;
  supportUrl?: string;
}
