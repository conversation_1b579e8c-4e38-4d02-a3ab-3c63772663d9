"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { AddTeamModal } from './add-team-modal'
import {
  Search,
  Plus,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  Filter,
  Users,
  DollarSign
} from 'lucide-react'
import { formatCurrency } from '@/utils/format'

// Types
interface Team {
  id: string
  name: string
  budget_cents?: number
  budget_used_cents?: number
  agent_count?: number
  created_at?: string
  organization_id: string
}

type SortField = 'name' | 'created_at' | 'budget' | 'agent_count';
type SortDirection = 'asc' | 'desc';

interface TeamsTableProps {
  teams: Team[]
  orgId: string
}

export function TeamsTable({ teams, orgId }: TeamsTableProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [sortField, setSortField] = useState<SortField>('created_at')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  // Budget data is now fetched from the database via the getOrganizationTeams function

  // Handle sort click
  const handleSortClick = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Navigate to agents page filtered by team
  const navigateToAgents = (teamId: string, teamName: string) => {
    router.push(`/dashboard/${orgId}/agents?team=${teamId}`)
  }

  // Filter and sort teams
  const filteredTeams = teams
    .filter(team => team.name.toLowerCase().includes(searchQuery.toLowerCase()))
    .sort((a, b) => {
      if (sortField === 'name') {
        const comparison = a.name.localeCompare(b.name)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'created_at') {
        const dateA = new Date(a.created_at || 0).getTime()
        const dateB = new Date(b.created_at || 0).getTime()
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA
      } else if (sortField === 'budget') {
        const budgetA = a.budget_cents || 0
        const budgetB = b.budget_cents || 0
        return sortDirection === 'asc' ? budgetA - budgetB : budgetB - budgetA
      } else if (sortField === 'agent_count') {
        const countA = a.agent_count || 0
        const countB = b.agent_count || 0
        return sortDirection === 'asc' ? countA - countB : countB - countA
      }
      return 0
    })

  return (
    <>
      <div className="flex items-center justify-between mb-4">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search teams..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button onClick={() => setShowAddModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Team
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('name')}
              >
                <div className="flex items-center">
                  Team Name
                  {sortField === 'name' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('agent_count')}
              >
                <div className="flex items-center">
                  Agents
                  {sortField === 'agent_count' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('budget')}
              >
                <div className="flex items-center">
                  Budget
                  {sortField === 'budget' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Budget Used
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Actions
                </div>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTeams.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                  {searchQuery ? 'No teams found matching your search.' : 'No teams found. Create your first team!'}
                </TableCell>
              </TableRow>
            ) : (
              filteredTeams.map((team) => (
                <TableRow key={team.id}>
                  <TableCell>
                    <div className="font-medium">{team.name}</div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-normal text-foreground"
                      onClick={() => navigateToAgents(team.id, team.name)}
                    >
                      <Users className="h-4 w-4 mr-1" />
                      {team.agent_count || 0}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                      {formatCurrency((team.budget_cents || 0) / 100)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                        {formatCurrency((team.budget_used_cents || 0) / 100)}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                        <div
                          className="bg-primary h-2.5 rounded-full"
                          style={{
                            width: `${Math.min(100, Math.round((team.budget_used_cents || 0) / (team.budget_cents || 1) * 100))}%`
                          }}
                        ></div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {Math.round((team.budget_used_cents || 0) / (team.budget_cents || 1) * 100)}% used
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => navigateToAgents(team.id, team.name)}>
                          View Agents
                        </DropdownMenuItem>
                        <DropdownMenuItem>Edit Team</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          Delete Team
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <AddTeamModal
        open={showAddModal}
        onOpenChange={setShowAddModal}
        orgId={orgId}
      />
    </>
  )
}

