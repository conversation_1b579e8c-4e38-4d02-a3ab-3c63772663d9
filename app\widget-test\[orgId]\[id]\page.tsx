'use client'

import React, { useEffect, useState, useCallback } from 'react'
import { createClient } from '@/utils/supabase/client'
import Script from 'next/script'
import { useParams } from 'next/navigation'
import { useWidgetStore } from '@/stores/widget-store'

export default function WidgetTestPage() {
  // Use the useParams hook to get params in client components
  const params = useParams();
  const id = params.id as string;
  const orgId = params.orgId as string;
  const [agentName, setAgentName] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [scriptLoaded, setScriptLoaded] = useState(false)
  const [widgetSettings, setWidgetSettings] = useState({
    position: 'bottom-right',
    customText: {},
    hideIcon: false,
    customIcon: '',
    customCSS: '',
    widgetType: 'standalone' as 'standalone' | 'compact'
  });

  // Fetch agent data
  useEffect(() => {
    const fetchAgentData = async () => {
      try {
        const supabase = createClient()
        const { data } = await supabase
          .from('agents')
          .select('name')
          .eq('id', id)
          .single()

        if (data) {
          setAgentName(data.name || 'Agent')
        }
      } catch (error) {
        console.error('Error fetching agent data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAgentData()
  }, [id])

  // Get widget settings from Zustand store
  const { getSettingsForAgent } = useWidgetStore();

  // Update local state from Zustand store
  useEffect(() => {
    const storeSettings = getSettingsForAgent(id);
    // Ensure widgetType is always defined
    setWidgetSettings({
      position: storeSettings.position || 'bottom-right',
      customText: storeSettings.customText || {},
      hideIcon: storeSettings.hideIcon || false,
      customIcon: storeSettings.customIcon || '',
      customCSS: storeSettings.customCSS || '',
      widgetType: storeSettings.widgetType || 'standalone'
    });

    // Listen for changes in widget settings
    const handleSettingsChanged = (event: Event) => {
      const detail = (event as CustomEvent).detail;

      // Only update if the event is for this agent
      if (!detail.agentId || detail.agentId === id) {
        console.log('Widget settings changed for this agent in test page:', detail);
        const updatedSettings = getSettingsForAgent(id);
        setWidgetSettings({
          position: updatedSettings.position || 'bottom-right',
          customText: updatedSettings.customText || {},
          hideIcon: updatedSettings.hideIcon || false,
          customIcon: updatedSettings.customIcon || '',
          customCSS: updatedSettings.customCSS || '',
          widgetType: updatedSettings.widgetType || 'standalone'
        });
      }
    };

    // Add event listener
    window.addEventListener('widget-settings-changed', handleSettingsChanged);

    // Cleanup
    return () => {
      window.removeEventListener('widget-settings-changed', handleSettingsChanged);
    };
  }, [id, getSettingsForAgent]);

  // Function to update the dynamic CSS
  const updateDynamicCSS = useCallback(() => {
    // Update the dynamic CSS with current settings
    const styleElement = document.getElementById('widget-dynamic-css');
    if (styleElement) {
      styleElement.innerHTML = `
        /* Widget Customization Styles */
        ::part(widget-container) {
          /* Container customization */
          background: white;
          border-radius: 16px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        ::part(start-button) {
          /* Start button style */
          background-color: #2e62c9;
          font-family: "Inter",Sans-serif;
          font-size: 16px;
          font-weight: 500;
          color: #fff;
          border-radius: 8px;
          box-shadow: inset 0 .5px .5px 0 rgba(255,255,255,.4);
        }

        ::part(end-button) {
          /* End button style */
          background-color: #e12525;
          font-family: "Inter",Sans-serif;
          color: #fff;
          border-radius: 8px;
        }

        ::part(avatar-container) {
          /* Avatar container style */
          color: white;
          background-color: #2e62c9;
        }

        ${widgetSettings.customCSS || ''}
      `;
    }
  }, [widgetSettings]);

  // Function to initialize or update the widget
  const initializeWidget = useCallback(() => {
    if (!scriptLoaded) return;

    try {
      // Update the dynamic CSS first
      updateDynamicCSS();

      // Clear the container first
      const container = document.getElementById('widget-container');
      if (container) {
        container.innerHTML = '';
      }

      // Initialize the widget with settings - exactly as in the documentation
      if (window.ConversationWidget) {
        // Use setTimeout to ensure the DOM is ready
        setTimeout(() => {
          console.log('Initializing widget with agentId:', id);
          console.log('Container exists:', !!document.getElementById('widget-container'));
          console.log('Widget settings:', widgetSettings);

          window.ConversationWidget({
            agentId: id,
            position: widgetSettings.position || 'bottom-right',
            containerId: 'widget-container',
            type: widgetSettings.widgetType || 'standalone',
            customParts: {
              // Element identifiers for styling
              container: 'widget-container',
              startButton: 'start-button',
              endButton: 'end-button',
              avatarContainer: 'avatar-container',
              statusText: 'status-text',

              // Icon customization
              ...(widgetSettings.hideIcon ? { hideIcon: true } : {}),
              ...(widgetSettings.customIcon ? { customIcon: widgetSettings.customIcon } : {}),

              // Text customization
              customText: widgetSettings.customText || {}
            }
          });

          console.log('Widget initialized successfully in test page');
        }, 100);
      } else {
        console.error('ConversationWidget is not available');
      }
    } catch (error) {
      console.error('Error initializing widget:', error)
    }
  }, [id, widgetSettings, scriptLoaded, updateDynamicCSS]);

  // Initialize widget when settings change
  useEffect(() => {
    // Update the dynamic CSS
    updateDynamicCSS();

    // Initialize the widget
    initializeWidget();

    // Cleanup
    return () => {
      // Clear the container
      const container = document.getElementById('widget-container');
      if (container) {
        container.innerHTML = '';
      }
    }
  }, [initializeWidget, updateDynamicCSS])

  // Handle script load
  const handleScriptLoad = () => {
    setScriptLoaded(true)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <Script
        src={`${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js`}
        onLoad={handleScriptLoad}
        strategy="afterInteractive"
      />
      <h1>a</h1>

      <style id="widget-dynamic-css"></style>

      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-2">
          {isLoading ? 'Loading...' : `Testing ${agentName}`}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          This is a test page for your widget. The {widgetSettings.widgetType || 'standalone'} widget should appear in the {widgetSettings.position.replace('-', ' ')} corner.
        </p>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Widget Information</h2>
          <ul className="space-y-2">
            <li><strong>Agent ID:</strong> {id}</li>
            <li><strong>Organization ID:</strong> {orgId}</li>
            <li><strong>Position:</strong> {widgetSettings.position}</li>
            <li><strong>Widget Type:</strong> {widgetSettings.widgetType || 'standalone'}</li>
            {widgetSettings.customCSS && <li><strong>Custom CSS:</strong> Applied</li>}
            {widgetSettings.hideIcon && <li><strong>Icon:</strong> Hidden</li>}
            {widgetSettings.customIcon && <li><strong>Icon:</strong> Custom</li>}
          </ul>

          <div className="mt-8">
            <h3 className="text-lg font-medium mb-2">Embed Code</h3>
            <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded overflow-x-auto text-sm">
{`<script src="${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js"></script>
<script>
  ConversationWidget({
    agentId: '${id}',
    position: '${widgetSettings.position}',
    containerId: 'widget-container',
    type: '${widgetSettings.widgetType || 'standalone'}'${(widgetSettings.hideIcon || widgetSettings.customIcon || Object.keys(widgetSettings.customText || {}).length > 0) ? `,
    customParts: {` : ''}${widgetSettings.hideIcon ? `
      hideIcon: true,` : ''}${widgetSettings.customIcon ? `
      customIcon: "[SVG Icon Code]",` : ''}${Object.keys(widgetSettings.customText || {}).length > 0 ? `
      customText: ${JSON.stringify(widgetSettings.customText, null, 2)}` : ''}${(widgetSettings.hideIcon || widgetSettings.customIcon || Object.keys(widgetSettings.customText || {}).length > 0) ? `
    }` : ''}
  });
</script>${widgetSettings.customCSS ? `
<style>
  /* Custom CSS would be included here */
</style>` : ''}
`}
            </pre>
          </div>
        </div>

        <div className="mt-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md min-h-[300px] relative">
          <h3 className="text-lg font-medium mb-4">Widget Preview</h3>

          {!scriptLoaded ? (
            <div className="absolute inset-0 flex items-center justify-center bg-blue-50 dark:bg-blue-900 rounded-lg">
              <p className="text-blue-700 dark:text-blue-200">Loading widget...</p>
            </div>
          ) : (
            <div id="widget-container" className="w-full h-[400px] relative border rounded-lg overflow-hidden">
              {/* The widget will be loaded here */}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
