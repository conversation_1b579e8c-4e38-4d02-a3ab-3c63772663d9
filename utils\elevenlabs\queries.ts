import { ElevenLabsClient } from 'elevenlabs';
import {
  ConversationalAiGetAgentsRequest,
  BodyPatchesAnAgentSettingsV1ConvaiAgentsAgentIdPatch,
  ConversationalAiGetConversationsRequest
} from 'elevenlabs/api';
import { env } from 'process';

const client = new ElevenLabsClient({ apiKey: env.ELEVENLABS_API_KEY });


// Agents







export const createAgent = (name:string, conversation_config: any) => {
  // const formattedName = `${name} [SB-${orgId}]`;

  return client.conversationalAi.createAgent({
    name,
    conversation_config
  });
};

export const getAgent = async (agent_id: string) => {
  const agent = await client.conversationalAi.getAgent(agent_id);
  return agent;
};
export const listAgents = async (params: ConversationalAiGetAgentsRequest = {}) => {
  const agents = await client.conversationalAi.getAgents(params);
  return agents;
};

export const findAgentsByNamePattern = async (namePattern: string) => {
  // Get all agents (ElevenLabs API doesn't support filtering by name pattern directly)
  const allAgents = await client.conversationalAi.getAgents({});

  // Filter agents by name pattern
  const matchingAgents = allAgents.agents.filter(agent =>
    agent.name.includes(namePattern)
  );

  return matchingAgents;
};
export const updateAgent = async (
  agent_id: string,
  name: string,
  conversation_config: any
) => {
  const config: BodyPatchesAnAgentSettingsV1ConvaiAgentsAgentIdPatch = {
    name,
    conversation_config
  };
  const agent = await client.conversationalAi.updateAgent(agent_id, config);
  return agent;
};

export const deleteAgent = async (agent_id: string) => {
  const agent = await client.conversationalAi.deleteAgent(agent_id);
  return agent;
};

// Conversations

export const listConversations = async (params: ConversationalAiGetConversationsRequest) => {
  const conversations = await client.conversationalAi.getConversations(params);
  return conversations;
};

export const getConversation = async (conversation_id: string) => {
  const conversation = await client.conversationalAi.getConversation(conversation_id);
  return conversation;
};

export const deleteConversation = async (conversation_id: string) => {
  const conversation = await client.conversationalAi.deleteConversation(conversation_id);
  return conversation;
};

export const getConversationAudio = async (conversation_id: string) => {
    const audio = await client.conversationalAi.getConversationAudio(conversation_id);
    return audio;
}

export const getSignedUrl = async (agent_id: string) => {
  const url = await client.conversationalAi.getSignedUrl({agent_id});
  return url;
}