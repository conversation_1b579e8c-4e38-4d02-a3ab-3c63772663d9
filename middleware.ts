import { type NextRequest } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';
import { handleTenantRouting } from '@/utils/tenant/middleware';

export async function middleware(request: NextRequest) {
  try {
    const { pathname, hostname } = new URL(request.url);
    console.log(`Middleware processing: ${hostname}${pathname}`);

    // First, handle tenant routing based on subdomain
    const tenantResponse = await handleTenantRouting(request);
    if (tenantResponse) {
      // If we got a response from tenant routing, use it
      console.log(`Tenant routing returned a response for: ${hostname}${pathname}`);
      return tenantResponse;
    }

    console.log(`No tenant routing, proceeding with session handling for: ${hostname}${pathname}`);
    // Otherwise, proceed with normal session handling
    return await updateSession(request);
  } catch (error) {
    console.error('Middleware error:', error);
    // In case of error, continue with the request without modification
    return request;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images - .svg, .png, .jpg, .jpeg, .gif, .webp
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'
  ]
};
