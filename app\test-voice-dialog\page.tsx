'use client'

import { useState } from 'react'
import { VoiceSelectionDialog } from '@/components/agents/settings/VoiceSelectionDialog'
import { Button } from '@/components/ui/button'

export default function TestVoiceDialogPage() {
  const [selectedVoiceId, setSelectedVoiceId] = useState('')

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Voice Selection Dialog Test</h1>
      
      <div className="space-y-4">
        <div>
          <p className="mb-2">Selected Voice ID: {selectedVoiceId || 'None'}</p>
          
          <VoiceSelectionDialog
            selectedVoiceId={selectedVoiceId}
            onVoiceSelect={setSelectedVoiceId}
            triggerComponent={
              <Button>Select Voice</Button>
            }
          />
        </div>
      </div>
    </div>
  )
}
