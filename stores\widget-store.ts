'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { WidgetSettings, defaultWidgetSettings } from '@/contexts/widget-context'

// Define the store type
interface WidgetStore {
  // Map of agent ID to widget settings
  agentSettings: Record<string, WidgetSettings>

  // Get settings for a specific agent
  getSettingsForAgent: (agentId: string) => WidgetSettings

  // Update settings for a specific agent
  updateWidgetSettings: (agentId: string, settings: Partial<WidgetSettings>) => void

  // Update custom text for a specific agent
  updateCustomText: (agentId: string, key: keyof WidgetSettings['customText'], value: string) => void
}

// Create the store with persistence
export const useWidgetStore = create<WidgetStore>()(
  persist(
    (set, get) => ({
      // Initial state
      agentSettings: {},

      // Get settings for a specific agent
      getSettingsForAgent: (agentId: string) => {
        const { agentSettings } = get()
        return agentSettings[agentId] || defaultWidgetSettings
      },

      // Update settings for a specific agent
      updateWidgetSettings: (agentId: string, settings: Partial<WidgetSettings>) => {
        set((state) => {
          const currentSettings = state.agentSettings[agentId] || defaultWidgetSettings
          const newSettings = { ...currentSettings, ...settings }

          // Dispatch an event to notify components that settings have changed
          // Use setTimeout to ensure the state is updated before dispatching the event
          if (typeof window !== 'undefined') {
            setTimeout(() => {
              window.dispatchEvent(new CustomEvent('widget-settings-changed', {
                detail: {
                  agentId,
                  settings: newSettings,
                  timestamp: Date.now()
                }
              }));
            }, 50);
          }

          return {
            agentSettings: {
              ...state.agentSettings,
              [agentId]: newSettings
            }
          }
        })
      },

      // Update custom text for a specific agent
      updateCustomText: (agentId: string, key: keyof WidgetSettings['customText'], value: string) => {
        set((state) => {
          const currentSettings = state.agentSettings[agentId] || defaultWidgetSettings
          const newSettings = {
            ...currentSettings,
            customText: {
              ...currentSettings.customText,
              [key]: value
            }
          }

          // Dispatch an event to notify components that settings have changed
          // Use setTimeout to ensure the state is updated before dispatching the event
          if (typeof window !== 'undefined') {
            setTimeout(() => {
              window.dispatchEvent(new CustomEvent('widget-settings-changed', {
                detail: {
                  agentId,
                  settings: newSettings,
                  timestamp: Date.now()
                }
              }));
            }, 50);
          }

          return {
            agentSettings: {
              ...state.agentSettings,
              [agentId]: newSettings
            }
          }
        })
      }
    }),
    {
      name: 'widget-settings-storage', // name of the item in localStorage
      partialize: (state) => ({ agentSettings: state.agentSettings }), // only persist agentSettings
    }
  )
)
