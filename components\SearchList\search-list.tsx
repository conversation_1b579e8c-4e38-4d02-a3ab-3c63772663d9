  'use client'

  import { cn } from "@/utils/cn"
  import Link from 'next/link'
  import { Input } from '@/components/ui/input'
  import { Button } from '@/components/ui/button'
  import { Search } from 'lucide-react'
  import { useState, useTransition } from 'react'
  import { useRouter, useSearchParams } from 'next/navigation'
  import { useDebounce } from '@/hooks/useDebounce'
  import { BaseItem, ListContext, ListResponse, ListState, GenericListProps } from '@/types/list'
  import { useInfiniteQuery } from '@tanstack/react-query'
  import { QueryProvider } from "@/providers/QueryProvider"

  interface ListSlots<T> {
    header?: React.ReactNode
    beforeSearch?: React.ReactNode
    afterSearch?: React.ReactNode
    beforeList?: React.ReactNode
    afterList?: React.ReactNode
    footer?: React.ReactNode
    itemContent?: React.ComponentType<{ item: T; isActive: boolean; context: ListContext }>
    emptyState?: React.ReactNode
  }
 

  async function fetchItems<T>({ 
    pageParam = 1, 
    queryKey,
    signal
  }: { 
    pageParam?: number 
    queryKey: (string | number | undefined)[]
    signal?: AbortSignal
  }): Promise<ListResponse<T>> {
    const [_, search, pageSize] = queryKey
    const params = new URLSearchParams({
      search: String(search),
      page: String(pageParam),
      pageSize: String(pageSize)
    })
    const fetchUrl = queryKey[3] as string
    console.log('Fetching with params:', params.toString())
    const response = await fetch(`${fetchUrl}?${params}`)
    console.log('Response:', response)
    return response.json() as Promise<ListResponse<T>>
  }

  function SearchListContent<T extends BaseItem>({
    context,
    fetchUrl,
    initialState,
    currentItemId,
    pageSize = 10,
    searchPlaceholder = "Search...",
    listTitle = "Items",
    slots = {}
  }: GenericListProps<T>) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [isPending, startTransition] = useTransition()
    const [search, setSearch] = useState(initialState.search)
    const debouncedSearch = useDebounce(search, 300)

    const {
      data,
      fetchNextPage,
      hasNextPage,
      isFetching,
      isFetchingNextPage,
      status
    } = useInfiniteQuery({
      queryKey: ['items', debouncedSearch, pageSize, fetchUrl],
      queryFn: fetchItems<T>,
      initialPageParam: initialState.page ?? 1,
      initialData: {
        pages: [{
          data: initialState.items,
          count: initialState.count,
          hasMore: initialState.hasMore,
          page: initialState.page ?? 1
        }],
        pageParams: [initialState.page]
      },
      getNextPageParam: (lastPage) => 
        lastPage.hasMore ? lastPage.page + 1 : undefined,
      staleTime: 0, // Consider data immediately stale
      refetchOnMount: false
    })

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      console.log("change", e.target.value)
      setSearch(e.target.value)
      startTransition(() => {
        const params = new URLSearchParams(searchParams)
        if (e.target.value) {
          params.set('search', e.target.value)
        } else {
          params.delete('search')
        }
        params.set('page', '1')
        // router.replace(`${context.basePath}?${params.toString()}`)
      })
    }

    const allItems = data?.pages.flatMap(page => page.data) ?? []
    const totalCount = data?.pages[0]?.count ?? 0

    const DefaultItemContent = ({ item, isActive }: { item: T; isActive: boolean }) => (
      <Link 
        className={cn(
          "block p-2 rounded text-sm",
          isActive ? "bg-slate-100 font-medium" : "hover:bg-slate-50"
        )}
        href={`${context.basePath}/${item.id}`}
      >
        {item.name}
      </Link>
    )

    return (
      <nav className="p-4 space-y-4">
        {slots.header}
        {slots.beforeSearch}

        <div className="relative">
          <Input
            type="search"
            value={search}
            onChange={handleSearchChange}
            placeholder={searchPlaceholder}
            className="pr-8"
          />
          <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
        </div>

        {slots.afterSearch}
        {slots.beforeList}

        <div className="space-y-2">
          {isFetching && allItems.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              Loading...
            </p>
          ) : allItems.length > 0 ? (
            allItems.map(item => {
              const ItemContent = slots.itemContent || DefaultItemContent
              return (
                <ItemContent 
                  key={item.id} 
                  item={item} 
                  isActive={item.id === currentItemId}
                  context={context}
                />
              )
            })
          ) : (
            slots.emptyState || (
              <p className="text-sm text-muted-foreground text-center py-4">
                No items found
              </p>
            )
          )}
        </div>

        {slots.afterList}

        {hasNextPage && (
          <Button 
            onClick={() => fetchNextPage()}
            variant="ghost" 
            className="w-full"
            disabled={isFetchingNextPage}
          >
            {isFetchingNextPage ? 'Loading more...' : 'Load More'}
          </Button>
        )}

        {slots.footer || (
          <p className="text-sm text-muted-foreground">
            Showing {allItems.length} of {totalCount} {listTitle.toLowerCase()}
          </p>
        )}
      </nav>
    )
  }

  export default function SearchList<T extends BaseItem>(props: GenericListProps<T>) {
    return (
      <QueryProvider>
        <SearchListContent {...props} />
      </QueryProvider>
    )
  }