"use client"

import { useRouter } from "next/navigation"
import { Suspense } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Building, Plus } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface Organization {
  id: string,
  name: string
}

// Loading skeleton for the organization selector
function OrganizationSelectorSkeleton() {
  return (
    <div className="w-full max-w-md space-y-4">
      <Skeleton className="h-10 w-full" />
    </div>
  )
}

// The actual selector component
function OrganizationSelectorContent({
  organizations,
  currentOrganization
}: {
  organizations: Organization[],
  currentOrganization: string
}) {
  const router = useRouter()

  const handleValueChange = (value: string) => {
    if (value === "create-new") {
      router.push("/dashboard/create-organization")
    } else {
      router.push(`/dashboard/${value}`)
    }
  }

  return (
    <Select defaultValue={currentOrganization} onValueChange={handleValueChange}>
      <SelectTrigger className="w-full">
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4" />
          <SelectValue placeholder="Select an organization" />
        </div>
      </SelectTrigger>
      <SelectContent>
        {organizations.map((organization) => (
          <SelectItem
            key={organization.id}
            value={organization.id}
          >
            <div className="flex items-center gap-2">
              <span>{organization.name}</span>
            </div>
          </SelectItem>
        ))}
        <SelectItem value="create-new" className="text-primary dark:text-primary border-t dark:border-zinc-700">
          <div className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            <span>✨ Create New Organization</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  )
}

// Main exported component with Suspense
export function OrganizationSelector({
  organizations,
  currentOrganization
}: {
  organizations: Organization[],
  currentOrganization: string
}) {
  return (
    <div className="w-full max-w-md space-y-4">
      <Suspense fallback={<OrganizationSelectorSkeleton />}>
        <OrganizationSelectorContent
          organizations={organizations}
          currentOrganization={currentOrganization}
        />
      </Suspense>
    </div>
  )
}