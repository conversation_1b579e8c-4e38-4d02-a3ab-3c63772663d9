import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// This endpoint is for admin use only to manually check and clean up orphaned organizations
export async function POST(request: Request) {
  try {
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Check if user is an admin (you may need to adjust this based on your admin role implementation)
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isAdmin) {
      return new NextResponse('Forbidden: Admin access required', { status: 403 });
    }

    // Find orphaned organizations (organizations with no members)
    const { data: orphanedOrgs, error: findError } = await supabase.rpc('find_orphaned_organizations');

    if (findError) {
      console.error('Error finding orphaned organizations:', findError);
      return new NextResponse('Internal Server Error', { status: 500 });
    }

    // If no orphaned organizations found
    if (!orphanedOrgs || orphanedOrgs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No orphaned organizations found',
        deleted: []
      });
    }

    // Delete each orphaned organization
    const deletedOrgs = [];
    for (const org of orphanedOrgs) {
      const { error: deleteError } = await supabase
        .from('organizations')
        .delete()
        .eq('id', org.id);

      if (deleteError) {
        console.error(`Error deleting organization ${org.id}:`, deleteError);
      } else {
        deletedOrgs.push(org);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Deleted ${deletedOrgs.length} orphaned organizations`,
      deleted: deletedOrgs
    });
  } catch (error) {
    console.error('Error in cleanup-orphaned-orgs route:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
