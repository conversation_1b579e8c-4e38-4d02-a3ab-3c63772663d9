import { Metadata } from 'next';
// import Footer from '@/components/ui/Footer';
// import Navbar from '@/components/ui/Navbar';
import { createClient } from '@/utils/supabase/server';
import {
  getUserDetails,
  getSubscription,
  getUser,
  getUserOrganizations
} from '@/utils/supabase/queries';

import { Toaster } from '@/components/ui/Toasts/toaster';
import { PropsWithChildren, Suspense } from 'react';
import { getURL } from '@/utils/helpers';
import 'styles/main.css';

const title = 'BotCom AI';
const description = 'AI-powered conversational agents for your business.';

export const metadata: Metadata = {
  metadataBase: new URL(getURL()),
  title: title,
  description: description,
  openGraph: {
    title: title,
    description: description
  }
};

import { AppSidebar } from "@/components/app-sidebar"
import { ChartAreaInteractive } from "@/components/chart-area-interactive"
import { DataTable } from "@/components/data-table"
import { SectionCards } from "@/components/section-cards"
import { SiteHeader } from "@/components/site-header"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"

import data from "./data.json"
import { redirect } from 'next/navigation';

interface Organization {
  id: string;
  name: string;
}
interface LayoutProps {
  params: Promise<{
    orgId: string
  }>,
  children: React.ReactNode
}

export default async function Layout({ children, params }: LayoutProps) {

  const {orgId} = await params;

  const supabase = await createClient();
 const [user, userDetails, subscription, organizations] = await Promise.all([
     getUser(supabase),
     getUserDetails(supabase),
     getSubscription(supabase),
     getUserOrganizations(supabase)
   ]);

   const currentOrganization = organizations.find(org => org.id === orgId)
   if(!currentOrganization) {
     redirect('/dashboard')
   }


  return (
    <>
        <main
          id="skip"
          className="text-zinc-900 dark:text-zinc-50"
        >
            <SidebarProvider>
              <AppSidebar variant="inset" user={user} organizations={organizations} currentOrganization={orgId}/>
              <SidebarInset>
                <SiteHeader />
                {children}
              </SidebarInset>
            </SidebarProvider>
        </main>
        <Suspense>
          <Toaster />
        </Suspense>
      </>



  )
}
