'use client';

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const faqs = [
  {
    question: "How are calls billed?",
    answer: "Calls are billed based on duration. Each plan includes a set number of minutes per month, and additional minutes are billed at a per-minute rate. You can monitor your usage in the dashboard."
  },
  {
    question: "Can I change plans?",
    answer: "Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the start of your next billing cycle."
  },
  {
    question: "Is there a free trial?",
    answer: "Yes, all paid plans come with a 14-day free trial. No credit card is required to start. You can explore all features during your trial period."
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards including Visa, Mastercard, American Express, and Discover. We also support payment via PayPal for annual subscriptions."
  },
  {
    question: "Can I get a refund?",
    answer: "We offer a 30-day money-back guarantee for all new subscriptions. If you're not satisfied with our service, contact our support team within 30 days of your purchase for a full refund."
  },
  {
    question: "What happens if I exceed my plan limits?",
    answer: "If you exceed your plan's call minutes, you'll be charged for additional usage at our standard overage rates. You can set up usage alerts in your account settings to help manage costs."
  }
];

export function PricingFAQ() {
  return (
    <div className="w-full max-w-3xl mx-auto">
      <h2 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h2>
      <Accordion type="single" collapsible className="w-full">
        {faqs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger className="text-left font-medium">{faq.question}</AccordionTrigger>
            <AccordionContent className="text-zinc-500 dark:text-zinc-400">
              {faq.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
