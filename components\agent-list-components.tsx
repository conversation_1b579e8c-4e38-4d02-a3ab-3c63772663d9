'use client'

import { But<PERSON> } from '@/components/ui/button'
import { BotIcon, Plus } from 'lucide-react'
import { cn } from '@/utils/cn'
import Link from 'next/link'
import { BaseItem, ListContext } from '@/types/list'


export function AgentItem({ 
  item, 
  isActive, 
  context 
}: { 
  item: BaseItem
  isActive: boolean
  context: ListContext 
}) {
  'use client'
  return (
    <Link 
      href={`${context.basePath}/${item.id}`}
      className={cn(
        "flex items-center gap-2 p-2 rounded",
        isActive ? "bg-slate-100" : "hover:bg-slate-50"
      )}
    >
      <BotIcon className="h-5 w-5 text-muted-foreground" />
      <p className={cn("font-medium", isActive && "text-primary")}>
        {item.name}
      </p>
    </Link>
  )
}