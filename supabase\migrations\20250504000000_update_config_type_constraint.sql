-- Drop the existing constraint
ALTER TABLE "public"."agent_configs"
DROP CONSTRAINT IF EXISTS "agent_configs_config_type_check";

-- Add the updated constraint with additional allowed values
ALTER TABLE "public"."agent_configs"
ADD CONSTRAINT "agent_configs_config_type_check"
CHECK ("config_type" = ANY (ARRAY['external'::"text", 'internal'::"text", 'elevenlabs'::"text", 'text_widget'::"text", 'native'::"text", 'global'::"text", 'voice'::"text"]));
