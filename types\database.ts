export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      agents: {
        Row: {
          id: string
          organization_id: string | null
          team_id: string | null
          name: string | null
          budget_cents: number | null
          billing_strategy: string | null
          per_second_cost_cents: number | null
          created_at: string | null
        }
        Insert: {
          id?: string
          organization_id?: string | null
          team_id?: string | null
          name?: string | null
          budget_cents?: number | null
          billing_strategy?: string | null
          per_second_cost_cents?: number | null
          created_at?: string | null
        }
        Update: {
          id?: string
          organization_id?: string | null
          team_id?: string | null
          name?: string | null
          budget_cents?: number | null
          billing_strategy?: string | null
          per_second_cost_cents?: number | null
          created_at?: string | null
        }
      }
      agent_configs: {
        Row: {
          id: string
          agent_id: string | null
          config_type: string
          provider?: string
          external_agent_url: string | null
          llm_provider: string | null
          llm_model: string | null
          voice_provider: string | null
          voice_model: string | null
          custom_metadata: Json
          created_at: string | null
        }
        Insert: {
          id?: string
          agent_id?: string | null
          config_type: string
          provider?: string
          external_agent_url?: string | null
          llm_provider?: string | null
          llm_model?: string | null
          voice_provider?: string | null
          voice_model?: string | null
          custom_metadata?: Json
          created_at?: string | null
        }
        Update: {
          id?: string
          agent_id?: string | null
          config_type?: string
          provider?: string
          external_agent_url?: string | null
          llm_provider?: string | null
          llm_model?: string | null
          voice_provider?: string | null
          voice_model?: string | null
          custom_metadata?: Json
          created_at?: string | null
        }
      }
    }
  }
}

export type AgentConfig = Database['public']['Tables']['agent_configs']['Row']
export type Agent = Database['public']['Tables']['agents']['Row']

export interface AgentVoiceMetadata {
  latencyOptimization?: number
  stability?: number
  speed?: number
  similarity?: number
  externalAgentId?: string
  voice?: string
// }

// export interface AgentConfig {
//   id: string;
//   agent_id: string;
//   config_type: string;
//   provider: string;
//   external_provider_id: string | null;
//   language: string;
//   prompt_config: {
//     prompt: string;
//     llm: string;
//     tools: any[];
//     knowledge_base: any[];
//     temperature: number;
//     max_tokens: number;
//   };
//   first_message: string;
//   dynamic_variables: {
//     dynamic_variable_placeholders: Record<string, any>;
//   };
//   asr_config: {
//     quality: string;
//     provider: string;
//     user_input_audio_format: string;
//     keywords: string[];
//   };
//   tts_config: {
//     model_id: string;
//     agent_output_audio_format: string;
//     optimize_streaming_latency: number;
//     stability: number;
//     similarity_boost: number;
//   };
//   turn_config: {
//     turn_timeout: number;
//     silence_end_call_timeout: number;
//   };
//   conversation_config: {
//     max_duration_seconds: number;
//     client_events: string[];
//   };
//   platform_settings: {
//     widget: {
//       variant: string;
//       avatar: {
//         type: string;
//         color_1: string;
//         color_2: string;
//       };
//       feedback_mode: string;
//       show_avatar_when_collapsed: boolean;
//     };
//     // ... other platform settings
//   };
//   created_at: string;
// 
}

