'use client'

import { useState, FormEvent } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/Toasts/use-toast'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'

interface UserSecurityFormProps {
  userId: string
  orgId: string
}

export function UserSecurityForm({ userId, orgId }: UserSecurityFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [errors, setErrors] = useState({ 
    currentPassword: '', 
    newPassword: '', 
    confirmPassword: '' 
  })
  
  // Two-factor authentication state
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)

  const validateForm = () => {
    const newErrors = { 
      currentPassword: '', 
      newPassword: '', 
      confirmPassword: '' 
    }
    let isValid = true

    if (!currentPassword) {
      newErrors.currentPassword = 'Current password is required.'
      isValid = false
    }

    if (newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters.'
      isValid = false
    }

    if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match.'
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  async function handleSubmit(e: FormEvent) {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // In a real implementation, you would update the user's password
      // For now, we'll just show a success message
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: 'Password updated',
        description: 'Your password has been updated successfully.',
      })

      // Clear form
      setCurrentPassword('')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error) {
      console.error('Error updating password:', error)
      toast({
        title: 'Error',
        description: 'Failed to update password. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  async function handleTwoFactorToggle() {
    const newState = !twoFactorEnabled
    
    try {
      // In a real implementation, you would enable/disable 2FA
      // For now, we'll just show a success message
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setTwoFactorEnabled(newState)
      
      toast({
        title: newState ? 'Two-factor authentication enabled' : 'Two-factor authentication disabled',
        description: newState 
          ? 'Your account is now more secure with two-factor authentication.' 
          : 'Two-factor authentication has been disabled for your account.',
      })
    } catch (error) {
      console.error('Error toggling 2FA:', error)
      toast({
        title: 'Error',
        description: 'Failed to update two-factor authentication settings. Please try again.',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Change Password</CardTitle>
          <CardDescription>Update your password to keep your account secure</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Current Password</Label>
              <Input 
                id="currentPassword"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Enter current password" 
              />
              {errors.currentPassword && (
                <p className="text-sm font-medium text-red-500">{errors.currentPassword}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input 
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter new password" 
              />
              {errors.newPassword && (
                <p className="text-sm font-medium text-red-500">{errors.newPassword}</p>
              )}
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Password must be at least 8 characters long.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input 
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm new password" 
              />
              {errors.confirmPassword && (
                <p className="text-sm font-medium text-red-500">{errors.confirmPassword}</p>
              )}
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Updating...' : 'Update Password'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Two-Factor Authentication</CardTitle>
          <CardDescription>Add an extra layer of security to your account</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="text-sm font-medium">Two-factor authentication</h4>
              <p className="text-sm text-muted-foreground">
                Protect your account with an additional verification step.
              </p>
            </div>
            <Switch
              checked={twoFactorEnabled}
              onCheckedChange={handleTwoFactorToggle}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Sessions</CardTitle>
          <CardDescription>Manage your active sessions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">Current Session</h4>
                <p className="text-sm text-muted-foreground">
                  This device, last active just now
                </p>
              </div>
              <Button variant="outline" size="sm" disabled>
                Current
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="text-sm font-medium">All Other Sessions</h4>
                <p className="text-sm text-muted-foreground">
                  Log out from all other devices
                </p>
              </div>
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => {
                  toast({
                    title: 'All other sessions logged out',
                    description: 'You have been logged out from all other devices.',
                  })
                }}
              >
                Log Out
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
