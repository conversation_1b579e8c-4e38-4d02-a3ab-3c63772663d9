/**
 * This script helps apply migrations
 * Run with: node scripts/apply-migrations.js
 */

const { execSync } = require('child_process');
const path = require('path');

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '..', 'supabase', 'migrations');

console.log('Applying Supabase migrations...');
try {
  // Apply migrations
  execSync('supabase migration up', { stdio: 'inherit' });
  
  console.log('\nMigrations applied successfully!');
  console.log('\nIf you need to reset the database completely, you can run:');
  console.log('supabase db reset');
} catch (error) {
  console.error('Error during migration application:', error);
}
