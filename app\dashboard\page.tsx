import { redirect } from 'next/navigation'
import { cookies } from 'next/headers'
import { getUserOrganizations } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'

export default async function DashboardRedirectPage() {
    const supabase = await createClient();
  const cookieStore = await cookies();
  const lastOrgId = cookieStore.get('last_org_id')?.value

  // Get all orgs the user is a member of
  const orgs:{id:any, name:string}[] = (await getUserOrganizations(supabase)).flat()

  if (!orgs || orgs.length === 0) {
    return <div className="p-4">You're not part of any organization.</div>
  }

  // If lastOrgId exists and user is still in it
  const last = orgs.find((org) => org.id === lastOrgId)
  const redirectOrg = last?.id || orgs[0].id

  redirect(`/dashboard/${redirectOrg}`)
}
