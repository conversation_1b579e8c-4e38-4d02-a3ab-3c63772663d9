import Link from 'next/link';

import Logo from '@/components/icons/Logo';
import GitHub from '@/components/icons/GitHub';

export default function Footer() {
  return (
    <footer className="mx-auto max-w-[1920px] px-6 bg-white dark:bg-zinc-950 border-t">
      <div className="grid grid-cols-1 gap-8 py-12 text-zinc-900 dark:text-zinc-50 transition-colors duration-150 border-b lg:grid-cols-12 border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950">
        <div className="col-span-1 lg:col-span-2">
          <Link
            href="/"
            className="flex items-center flex-initial font-bold md:mr-24"
          >
            <span className="mr-2 border rounded-full border-zinc-700">
              <Logo />
            </span>
            <span>BotCom AI</span>
          </Link>
        </div>
        <div className="col-span-1 lg:col-span-2">
          <ul className="flex flex-col flex-initial md:flex-1">
            <li className="py-3 md:py-0 md:pb-4">
              <Link
                href="/"
                className="text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300"
              >
                Home
              </Link>
            </li>
            <li className="py-3 md:py-0 md:pb-4">
              <Link
                href="/"
                className="text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300"
              >
                About
              </Link>
            </li>
            <li className="py-3 md:py-0 md:pb-4">
              <Link
                href="/"
                className="text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300"
              >
                Careers
              </Link>
            </li>
            <li className="py-3 md:py-0 md:pb-4">
              <Link
                href="/"
                className="text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300"
              >
                Blog
              </Link>
            </li>
          </ul>
        </div>
        <div className="col-span-1 lg:col-span-2">
          <ul className="flex flex-col flex-initial md:flex-1">
            <li className="py-3 md:py-0 md:pb-4">
              <p className="font-bold text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300">
                LEGAL
              </p>
            </li>
            <li className="py-3 md:py-0 md:pb-4">
              <Link
                href="/"
                className="text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300"
              >
                Privacy Policy
              </Link>
            </li>
            <li className="py-3 md:py-0 md:pb-4">
              <Link
                href="/"
                className="text-zinc-900 dark:text-zinc-50 transition duration-150 ease-in-out hover:text-zinc-700 dark:hover:text-zinc-300"
              >
                Terms of Use
              </Link>
            </li>
          </ul>
        </div>
        <div className="flex items-start col-span-1 text-zinc-900 dark:text-zinc-50 lg:col-span-6 lg:justify-end">
          <div className="flex items-center h-10 space-x-6">
            <a
              aria-label="Github Repository"
              href="https://github.com/vercel/nextjs-subscription-payments"
            >
              <GitHub />
            </a>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center justify-between py-12 space-y-4 md:flex-row bg-white dark:bg-zinc-950">
        <div>
          <span>
            &copy; {new Date().getFullYear()} BotCom AI, All rights reserved.
          </span>
        </div>
        <div className="flex items-center">

        </div>
      </div>
    </footer>
  );
}
