// Test script for orphaned organization deletion
// Run this with: node scripts/test_orphaned_orgs.js

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables from .env file
require('dotenv').config({ path: path.resolve(process.cwd(), '.env.local') });

// Check if required environment variables are set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Required environment variables are missing.');
  console.error('Please make sure the following variables are set in your .env.local file:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testOrphanedOrgDeletion() {
  console.log('Testing orphaned organization deletion...');

  try {
    // 1. Create a test user
    console.log('Creating test user...');
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: `test-${Date.now()}@example.com`,
      password: 'password123',
      email_confirm: true
    });

    if (userError) {
      throw new Error(`Failed to create test user: ${userError.message}`);
    }

    const userId = userData.user.id;
    console.log(`Test user created with ID: ${userId}`);

    // 2. Create a test organization
    console.log('Creating test organization...');
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .insert([{ name: `Test Org ${Date.now()}` }])
      .select()
      .single();

    if (orgError) {
      throw new Error(`Failed to create test organization: ${orgError.message}`);
    }

    const orgId = orgData.id;
    console.log(`Test organization created with ID: ${orgId}`);

    // 3. Create a membership for the test user in the test organization
    console.log('Creating membership...');
    const { data: membershipData, error: membershipError } = await supabase
      .from('organization_memberships')
      .insert([{
        organization_id: orgId,
        user_id: userId,
        role: 'admin'
      }])
      .select()
      .single();

    if (membershipError) {
      throw new Error(`Failed to create membership: ${membershipError.message}`);
    }

    console.log(`Membership created with ID: ${membershipData.id}`);

    // 4. Create a credit wallet for the organization
    console.log('Creating credit wallet...');
    const { error: walletError } = await supabase
      .from('credit_wallets')
      .insert([{
        organization_id: orgId,
        balance_cents: 1000
      }]);

    if (walletError) {
      throw new Error(`Failed to create credit wallet: ${walletError.message}`);
    }

    // 5. Create a team for the organization
    console.log('Creating team...');
    const { data: teamData, error: teamError } = await supabase
      .from('agent_teams')
      .insert([{
        organization_id: orgId,
        name: 'Test Team',
        budget_cents: 500
      }])
      .select()
      .single();

    if (teamError) {
      throw new Error(`Failed to create team: ${teamError.message}`);
    }

    const teamId = teamData.id;
    console.log(`Team created with ID: ${teamId}`);

    // 6. Create an agent for the organization
    console.log('Creating agent...');
    const { error: agentError } = await supabase
      .from('agents')
      .insert([{
        organization_id: orgId,
        team_id: teamId,
        name: 'Test Agent',
        budget_cents: 200
      }]);

    if (agentError) {
      throw new Error(`Failed to create agent: ${agentError.message}`);
    }

    // 7. Verify that the organization and related data exist
    console.log('Verifying organization exists...');
    const { data: orgCheck, error: orgCheckError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', orgId)
      .single();

    if (orgCheckError || !orgCheck) {
      throw new Error(`Failed to verify organization: ${orgCheckError?.message || 'Not found'}`);
    }

    console.log('Organization exists, now deleting the membership...');

    // 8. Delete the membership (this should trigger the deletion of the orphaned organization)
    const { error: deleteError } = await supabase
      .from('organization_memberships')
      .delete()
      .eq('organization_id', orgId)
      .eq('user_id', userId);

    if (deleteError) {
      throw new Error(`Failed to delete membership: ${deleteError.message}`);
    }

    console.log('Membership deleted, waiting for trigger to execute...');

    // Wait a moment for the trigger to execute
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 9. Verify that the organization has been deleted
    console.log('Verifying organization has been deleted...');
    const { data: orgAfterDelete, error: orgAfterDeleteError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', orgId)
      .single();

    if (orgAfterDeleteError && orgAfterDeleteError.code === 'PGRST116') {
      console.log('SUCCESS: Organization was deleted as expected!');
    } else if (orgAfterDelete) {
      console.log('FAILURE: Organization still exists after membership deletion!');

      // Check if the trigger function exists
      console.log('Checking if the trigger function exists...');
      const { data: functionExists, error: functionError } = await supabase
        .rpc('function_exists', { function_name: 'delete_orphaned_organization' });

      console.log(`Function exists: ${functionExists || 'unknown'}, Error: ${functionError?.message || 'none'}`);

      // Check if there are any memberships for this organization
      console.log('Checking for remaining memberships...');
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_memberships')
        .select('id, user_id')
        .eq('organization_id', orgId);

      console.log(`Memberships: ${JSON.stringify(memberships || [])}`);
      console.log(`Membership error: ${membershipError?.message || 'none'}`);

      // Try to manually delete the organization
      console.log('Attempting to manually delete the organization...');
      const { error: manualDeleteError } = await supabase
        .from('organizations')
        .delete()
        .eq('id', orgId);

      console.log(`Manual deletion error: ${manualDeleteError?.message || 'none'}`);
    } else {
      console.log(`Error checking organization: ${orgAfterDeleteError?.message}`);
    }

    // 10. Clean up the test user
    console.log('Cleaning up test user...');
    const { error: deleteUserError } = await supabase.auth.admin.deleteUser(userId);

    if (deleteUserError) {
      console.log(`Warning: Failed to delete test user: ${deleteUserError.message}`);
    } else {
      console.log('Test user deleted successfully');
    }

    console.log('Test completed!');

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testOrphanedOrgDeletion();
