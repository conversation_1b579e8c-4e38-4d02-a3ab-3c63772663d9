import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * ElevenLabs post-call webhook handler
 *
 * This webhook receives data from ElevenLabs after a call is completed.
 * It processes the transcript, updates credit balances, and stores conversation data.
 */
export async function POST(request: Request) {
  try {
    // Get the agent ID from the query parameters
    const url = new URL(request.url);
    const agentId = url.searchParams.get('agentId');

    if (!agentId) {
      return NextResponse.json(
        { error: 'Missing agentId parameter' },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await request.json();
    console.log('Received post-call webhook:', JSON.stringify(body, null, 2));

    // Validate the webhook format
    if (!body.type || body.type !== 'post_call_transcription' || !body.data) {
      console.error('Invalid webhook format:', body);
      return NextResponse.json(
        { error: 'Invalid webhook format. Expected ElevenLabs post_call_transcription format.' },
        { status: 400 }
      );
    }

    // Extract data from the ElevenLabs format
    const { data } = body;
    const conversationId = data.conversation_id;
    const durationSeconds = data.metadata.call_duration_secs;
    const costCents = data.metadata.cost;
    const transcript = data.transcript;
    const successStatus = data.analysis.call_successful;
    const summary = data.analysis.transcript_summary;

    // Create Supabase client
    const supabase = await createClient();

    // Call the finalize_call function to deduct credits and record the transaction
    const { data: callResult, error: callError } = await supabase.rpc('finalize_call', {
      agent: agentId,
      cost: costCents,
      conversation: conversationId,
      duration_secs: durationSeconds
    });

    if (callError) {
      console.error('Error finalizing call:', callError);
      return NextResponse.json(
        {
          success: false,
          error: 'Error finalizing call',
          details: callError.message
        },
        { status: 500 }
      );
    }

    // Store conversation data
    try {
      // Format start time from Unix timestamp
      const startTime = new Date(data.metadata.start_time_unix_secs * 1000).toISOString();

      // Store the conversation and transcript
      await supabase.rpc('insert_conversation_with_messages', {
        _id: conversationId,
        _agent_id: agentId,
        _status: 'completed',
        _start_time: startTime,
        _duration_secs: durationSeconds,
        _cost_cents: costCents,
        _summary: summary || '',
        _success_status: successStatus || 'success',
        _transcript: transcript,
        _metadata: body,
        _analysis: data.analysis
      });

      console.log('Conversation stored successfully:', conversationId);
    } catch (convError) {
      console.error('Error storing conversation:', convError);
      // Continue execution even if conversation storage fails
    }

    // Return success response
    return NextResponse.json({
      success: callResult === true,
      conversation_id: conversationId,
      message: callResult === true ? 'Call finalized successfully' : 'Failed to finalize call'
    });
  } catch (error) {
    console.error('Error in post-call webhook:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process post-call webhook',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
