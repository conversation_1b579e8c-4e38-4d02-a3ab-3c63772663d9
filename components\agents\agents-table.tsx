'use client'

import { useState, useMemo, useEffect, useTransition } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Plus, MoreHorizontal, ChevronDown, ChevronUp, Filter, CheckCircle2, CircleDashed, Globe, Users, DollarSign } from 'lucide-react'
import { formatCurrency } from '@/utils/format'
import Link from 'next/link'
import { BaseItem } from '@/types/list'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { AddAgentPanel } from './add-agent-modal'
import { DeleteAgentButton } from './delete-agent-button'
import { DuplicateAgentButton } from './duplicate-agent-button'
import { LanguageFlag, languages, LanguageCode } from './language-flag'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface AgentsTableProps {
  agents: BaseItem[]
  orgId: string
  initialTeamFilter?: string
}

type SortField = 'name' | 'created_at' | 'team' | 'budget';
type SortDirection = 'asc' | 'desc';
type AgentStatus = 'draft' | 'published';

export function AgentsTable({ agents, orgId, initialTeamFilter }: AgentsTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [, startTransition] = useTransition() // We only need startTransition

  // Initialize state from URL parameters
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '')
  const [showAddPanel, setShowAddPanel] = useState(false)
  const [sortField, setSortField] = useState<SortField>(
    (searchParams.get('sort') as SortField) || 'created_at'
  )
  const [sortDirection, setSortDirection] = useState<SortDirection>(
    (searchParams.get('direction') as SortDirection) || 'desc'
  )
  const [creatorFilter, setCreatorFilter] = useState<string | null>(
    searchParams.get('creator')
  )
  const [statusFilter, setStatusFilter] = useState<AgentStatus | null>(
    searchParams.get('status') as AgentStatus | null
  )
  const [languageFilter, setLanguageFilter] = useState<LanguageCode | null>(
    searchParams.get('language') as LanguageCode | null
  )
  const [teamFilter, setTeamFilter] = useState<string | null>(
    searchParams.get('team') || initialTeamFilter || null
  )

  // Use a consistent status assignment for all agents
  const [agentStatuses, setAgentStatuses] = useState<Record<string, AgentStatus>>(() => {
    const statuses: Record<string, AgentStatus> = {}
    agents.forEach(agent => {
      // Always set to 'published' for consistency between server and client
      statuses[agent.id] = 'published'
    })
    return statuses
  })

  // Update agentStatuses when agents change
  useEffect(() => {
    // Check if there are any agents not in agentStatuses
    const newAgents = agents.filter(agent => !agentStatuses[agent.id])

    if (newAgents.length > 0) {
      setAgentStatuses(prev => {
        const updated = { ...prev }

        // Add new agents with default status
        newAgents.forEach(agent => {
          updated[agent.id] = 'published'
        })

        return updated
      })
    }
  }, [agents, agentStatuses])

  // Budget data is now fetched from the database via the getOrganizationAgents function

  // Get unique teams from agents for filtering
  const [teamsList, setTeamsList] = useState<{id: string, name: string}[]>(() => {
    const teamsMap = new Map<string, {id: string, name: string}>();

    agents.forEach(agent => {
      if (agent.team) {
        teamsMap.set(agent.team.id, agent.team);
      }
    });

    return Array.from(teamsMap.values());
  })

  // Update teamsList when agents change
  useEffect(() => {
    // Create a map of current teams
    const currentTeamsMap = new Map<string, {id: string, name: string}>();
    teamsList.forEach(team => {
      currentTeamsMap.set(team.id, team);
    });

    // Check for new teams
    let hasNewTeams = false;
    agents.forEach(agent => {
      if (agent.team && !currentTeamsMap.has(agent.team.id)) {
        currentTeamsMap.set(agent.team.id, agent.team);
        hasNewTeams = true;
      }
    });

    // Update state if new teams were found
    if (hasNewTeams) {
      setTeamsList(Array.from(currentTeamsMap.values()));
    }
  }, [agents, teamsList])

  // Use a consistent language assignment for all agents
  const [agentLanguages, setAgentLanguages] = useState<Record<string, { primary: LanguageCode, additional: LanguageCode[] }>>(() => {
    const langs: Record<string, { primary: LanguageCode, additional: LanguageCode[] }> = {}

    // Assign English as primary language and no additional languages for all agents
    // This ensures consistent rendering between server and client
    agents.forEach(agent => {
      langs[agent.id] = { primary: 'en', additional: [] }
    })

    return langs
  })

  // Update agentLanguages when agents change
  useEffect(() => {
    // Check if there are any agents not in agentLanguages
    const newAgents = agents.filter(agent => !agentLanguages[agent.id])

    if (newAgents.length > 0) {
      setAgentLanguages(prev => {
        const updated = { ...prev }

        // Add new agents with default language settings
        newAgents.forEach(agent => {
          updated[agent.id] = { primary: 'en', additional: [] }
        })

        return updated
      })
    }
  }, [agents, agentLanguages])

  // Update URL with current filter state
  useEffect(() => {
    startTransition(() => {
      const params = new URLSearchParams()

      // Add all filters to URL
      if (searchQuery) params.set('search', searchQuery)
      if (sortField !== 'created_at') params.set('sort', sortField)
      if (sortDirection !== 'desc') params.set('direction', sortDirection)
      if (creatorFilter) params.set('creator', creatorFilter)
      if (statusFilter) params.set('status', statusFilter)
      if (languageFilter) params.set('language', languageFilter)
      if (teamFilter) params.set('team', teamFilter)

      // Update URL without refreshing the page
      const url = `${window.location.pathname}?${params.toString()}`
      router.replace(url, { scroll: false })
    })
  }, [searchQuery, sortField, sortDirection, creatorFilter, statusFilter, languageFilter, teamFilter, router])

  // Toggle agent status
  const toggleAgentStatus = (agentId: string) => {
    setAgentStatuses(prev => ({
      ...prev,
      [agentId]: prev[agentId] === 'published' ? 'draft' : 'published'
    }))
  }

  // Handle sort click
  const handleSortClick = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new field and default to ascending
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return ''

    try {
      // Parse the date
      const date = new Date(dateString)

      // Get today and yesterday for comparison
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      // Format based on how recent the date is
      if (date.toDateString() === today.toDateString()) {
        // Today - show time only
        return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`
      } else if (date.toDateString() === yesterday.toDateString()) {
        // Yesterday - show 'Yesterday' and time
        return `Yesterday, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`
      } else {
        // Other dates - show month, day and time
        const options: Intl.DateTimeFormatOptions = {
          month: 'short',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        }
        return date.toLocaleDateString('en-US', options)
      }
    } catch (error) {
      return dateString
    }
  }

  // Filter and sort agents
  const filteredAndSortedAgents = useMemo(() => {
    // First filter by search query
    let result = agents.filter(agent =>
      agent.name.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Then filter by creator if set
    if (creatorFilter) {
      // For now, just return all agents since we don't have real creator data
      // In a real app, you would filter by the actual creator field
    }

    // Then filter by status if set
    if (statusFilter) {
      result = result.filter(agent => agentStatuses[agent.id] === statusFilter)
    }

    // Then filter by language if set
    if (languageFilter) {
      result = result.filter(agent => {
        const agentLang = agentLanguages[agent.id]
        return agentLang.primary === languageFilter || agentLang.additional.includes(languageFilter)
      })
    }

    // Then filter by team if set
    if (teamFilter) {
      result = result.filter(agent => agent.team?.id === teamFilter)
    }

    // Then sort
    return result.sort((a, b) => {
      if (sortField === 'name') {
        const comparison = a.name.localeCompare(b.name)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'created_at') {
        const dateA = new Date(a.created_at || 0).getTime()
        const dateB = new Date(b.created_at || 0).getTime()
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA
      } else if (sortField === 'team') {
        const teamA = a.team?.name || ''
        const teamB = b.team?.name || ''
        const comparison = teamA.localeCompare(teamB)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'budget') {
        const budgetA = a.budget_cents || 0
        const budgetB = b.budget_cents || 0
        return sortDirection === 'asc' ? budgetA - budgetB : budgetB - budgetA
      }
      return 0
    })
  }, [agents, searchQuery, sortField, sortDirection, creatorFilter, statusFilter, languageFilter, teamFilter, agentStatuses, agentLanguages])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Agents</h1>
          <p className="text-muted-foreground">Create and manage your AI agents</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowAddPanel(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New agent
          </Button>
        </div>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search agents..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value)
          }}
        />
      </div>

      <div className="flex items-center gap-2 mb-4">
        {creatorFilter && (
          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
            <span>Creator: {creatorFilter}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1 p-0"
              onClick={() => setCreatorFilter(null)}
            >
              <span className="sr-only">Remove filter</span>
              ×
            </Button>
          </Badge>
        )}

        {statusFilter && (
          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
            <span>Status: {statusFilter}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1 p-0"
              onClick={() => setStatusFilter(null)}
            >
              <span className="sr-only">Remove filter</span>
              ×
            </Button>
          </Badge>
        )}

        {languageFilter && (
          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
            <span className="flex items-center gap-1">
              <span>Language: </span>
              <LanguageFlag languageCode={languageFilter} size="sm" showTooltip={false} />
              {languages.find(l => l.code === languageFilter)?.name}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1 p-0"
              onClick={() => setLanguageFilter(null)}
            >
              <span className="sr-only">Remove filter</span>
              ×
            </Button>
          </Badge>
        )}

        {teamFilter && (
          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
            <span>Team: {teamsList.find(t => t.id === teamFilter)?.name}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1 p-0"
              onClick={() => setTeamFilter(null)}
            >
              <span className="sr-only">Remove filter</span>
              ×
            </Button>
          </Badge>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              <DropdownMenuLabel>Status</DropdownMenuLabel>
              <DropdownMenuCheckboxItem
                checked={statusFilter === 'published'}
                onCheckedChange={(checked) => {
                  setStatusFilter(checked ? 'published' : null)
                }}
              >
                Published
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilter === 'draft'}
                onCheckedChange={(checked) => {
                  setStatusFilter(checked ? 'draft' : null)
                }}
              >
                Draft
              </DropdownMenuCheckboxItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuLabel>Languages</DropdownMenuLabel>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Globe className="h-4 w-4 mr-2" />
                  Select Language
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent className="max-h-[300px] overflow-y-auto">
                  {languages.map(lang => (
                    <DropdownMenuCheckboxItem
                      key={lang.code}
                      checked={languageFilter === lang.code}
                      onCheckedChange={(checked) => {
                        setLanguageFilter(checked ? lang.code as LanguageCode : null)
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </div>
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuLabel>Team</DropdownMenuLabel>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Users className="h-4 w-4 mr-2" />
                  Select Team
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  {teamsList.map(team => (
                    <DropdownMenuCheckboxItem
                      key={team.id}
                      checked={teamFilter === team.id}
                      onCheckedChange={(checked) => {
                        setTeamFilter(checked ? team.id : null)
                      }}
                    >
                      {team.name}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            </DropdownMenuGroup>

            {(statusFilter || creatorFilter || languageFilter || teamFilter) && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => {
                  // Clear all filters
                  setStatusFilter(null)
                  setCreatorFilter(null)
                  setLanguageFilter(null)
                  setTeamFilter(null)

                  // Clear URL params immediately
                  startTransition(() => {
                    router.replace(window.location.pathname, { scroll: false })
                  })
                }}>
                  Clear all filters
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="w-[400px] cursor-pointer"
                onClick={() => handleSortClick('name')}
              >
                <div className="flex items-center">
                  Name
                  {sortField === 'name' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Created by
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="ml-1 h-6 w-6">
                        <Filter className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem onClick={() => setCreatorFilter(creatorFilter === 'Gautam' ? null : 'Gautam')}>Filter by Gautam</DropdownMenuItem>
                      {creatorFilter && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => setCreatorFilter(null)}>Clear filter</DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Languages
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('team')}
              >
                <div className="flex items-center">
                  Team
                  {sortField === 'team' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('budget')}
              >
                <div className="flex items-center">
                  Budget
                  {sortField === 'budget' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Budget Used
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Status
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('created_at')}
              >
                <div className="flex items-center">
                  Created at
                  {sortField === 'created_at' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedAgents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                  No agents found
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedAgents.map((agent) => (
                <TableRow key={agent.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="outline"
                        className={`w-2 h-2 p-0 rounded-full ${agentStatuses[agent.id] === 'published' ? 'bg-green-500 border-green-500 dark:bg-green-400 dark:border-green-400' : 'bg-amber-500 border-amber-500 dark:bg-amber-400 dark:border-amber-400'}`}
                      />
                      <Link
                        href={`/dashboard/${orgId}/agents/${agent.id}`}
                        className="font-medium hover:underline"
                      >
                        {agent.name}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-normal text-foreground"
                      onClick={() => setCreatorFilter('Gautam')}
                    >
                      Gautam
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="flex -space-x-1">
                        {/* Primary language with special styling */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="p-0 h-auto"
                          onClick={() => setLanguageFilter(agentLanguages[agent.id].primary)}
                        >
                          <LanguageFlag
                            languageCode={agentLanguages[agent.id].primary}
                            size="sm"
                            className="border-2 border-primary"
                          />
                        </Button>

                        {/* Additional languages */}
                        {agentLanguages[agent.id].additional.slice(0, 2).map(langCode => (
                          <Button
                            key={langCode}
                            variant="ghost"
                            size="icon"
                            className="p-0 h-auto"
                            onClick={() => setLanguageFilter(langCode)}
                          >
                            <LanguageFlag languageCode={langCode} size="sm" />
                          </Button>
                        ))}

                        {/* Show +X more if there are more languages */}
                        {agentLanguages[agent.id].additional.length > 2 && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center justify-center rounded-full bg-muted border h-6 w-6 text-xs ml-1 cursor-pointer"
                                  onClick={() => setLanguageFilter(agentLanguages[agent.id].additional[2])}
                                >
                                  +{agentLanguages[agent.id].additional.length - 2}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="space-y-1">
                                  {agentLanguages[agent.id].additional.slice(2).map((code) => {
                                    const lang = languages.find(l => l.code === code)
                                    return (
                                      <p key={code} className="flex items-center gap-2 cursor-pointer hover:text-primary"
                                        onClick={() => setLanguageFilter(code)}
                                      >
                                        <span>{lang?.flag}</span> {lang?.name}
                                      </p>
                                    )
                                  })}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {agent.team ? (
                      <Button
                        variant="link"
                        className="p-0 h-auto font-normal text-foreground"
                        onClick={() => setTeamFilter(agent.team?.id || null)}
                      >
                        {agent.team.name}
                      </Button>
                    ) : (
                      <span className="text-muted-foreground">No team</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                      {formatCurrency((agent.budget_cents || 0) / 100)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                        {formatCurrency((agent.budget_used_cents || 0) / 100)}
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mt-2">
                        <div
                          className="bg-primary h-2.5 rounded-full"
                          style={{
                            width: `${Math.min(100, Math.round((agent.budget_used_cents || 0) / (agent.budget_cents || 1) * 100))}%`
                          }}
                        ></div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {Math.round((agent.budget_used_cents || 0) / (agent.budget_cents || 1) * 100)}% used
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {agentStatuses[agent.id] === 'published' ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800 flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3" />
                          Published
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800 flex items-center gap-1">
                          <CircleDashed className="h-3 w-3" />
                          Draft
                        </Badge>
                      )}
                      <Switch
                        checked={agentStatuses[agent.id] === 'published'}
                        onCheckedChange={() => toggleAgentStatus(agent.id)}
                      />
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(agent.created_at || '')}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Link
                            href={`/dashboard/${orgId}/agents/${agent.id}`}
                            className="w-full"
                          >
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <DuplicateAgentButton
                            agentId={agent.id}
                            orgId={orgId}
                            agentName={agent.name}
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start px-2"
                            onSuccess={() => {
                              // No need to refresh since we're navigating away
                            }}
                          />
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <DeleteAgentButton
                            agentId={agent.id}
                            orgId={orgId}
                            agentName={agent.name}
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start px-2 text-red-600"
                            onSuccess={() => {
                              // Refresh the agents list
                              router.refresh()
                            }}
                          />
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <AddAgentPanel
        open={showAddPanel}
        onOpenChange={setShowAddPanel}
        orgId={orgId}
      />
    </div>
  )
}
