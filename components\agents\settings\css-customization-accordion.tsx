'use client'

import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

// Define the customization options for each part
export interface PartCustomization {
  backgroundColor?: string;
  textColor?: string;
  borderRadius?: number;
  borderColor?: string;
  borderWidth?: number;
  padding?: number;
  // Additional specific properties based on part type
  hoverBackgroundColor?: string;
  hoverTextColor?: string;
  fontWeight?: 'normal' | 'bold';
  fontSize?: number;
  fontFamily?: string;
  shadowEffect?: boolean;
  shadowIntensity?: number;
  maxWidth?: number;
}

// Main state object with all customizable parts
export interface CssCustomizationState {
  parts: {
    'widget-container': PartCustomization;
    'widget': PartCustomization;
    'start-button': PartCustomization;
    'end-button': PartCustomization;
    'avatar-container': PartCustomization;
    'status-text': PartCustomization;
    'minimize-button': PartCustomization;
    'message-container': PartCustomization;
    'user-message': PartCustomization;
    'ai-message': PartCustomization;
    'message-text': PartCustomization;
    'input-container': PartCustomization;
    'input-field': PartCustomization;
    'send-button': PartCustomization;
  }
}

// Default empty state
const defaultCustomizationState: CssCustomizationState = {
  parts: {
    'widget-container': {},
    'widget': {},
    'start-button': {},
    'end-button': {},
    'avatar-container': {},
    'status-text': {},
    'minimize-button': {},
    'message-container': {},
    'user-message': {},
    'ai-message': {},
    'message-text': {},
    'input-container': {},
    'input-field': {},
    'send-button': {},
  }
};

// Font family options
const fontFamilyOptions = [
  { value: 'inherit', label: 'Default' },
  { value: 'Arial, sans-serif', label: 'Arial' },
  { value: 'Helvetica, sans-serif', label: 'Helvetica' },
  { value: 'Verdana, sans-serif', label: 'Verdana' },
  { value: 'Tahoma, sans-serif', label: 'Tahoma' },
  { value: 'Trebuchet MS, sans-serif', label: 'Trebuchet MS' },
  { value: 'Times New Roman, serif', label: 'Times New Roman' },
  { value: 'Georgia, serif', label: 'Georgia' },
  { value: 'Garamond, serif', label: 'Garamond' },
  { value: 'Courier New, monospace', label: 'Courier New' },
  { value: 'Brush Script MT, cursive', label: 'Brush Script MT' },
];

interface CssCustomizationAccordionProps {
  value: string;
  onChange: (css: string) => void;
}

export function CssCustomizationAccordion({ value, onChange }: CssCustomizationAccordionProps) {
  // State for the customization options
  const [customization, setCustomization] = useState<CssCustomizationState>(defaultCustomizationState);

  // Flag to prevent initial CSS parsing from triggering onChange
  const initialParsingDoneRef = useRef(false);

  // Store the onChange function in a ref to prevent it from causing re-renders
  const onChangeRef = useRef(onChange);
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Function to update a part's customization
  const updatePart = useCallback((partName: keyof CssCustomizationState['parts'], updates: Partial<PartCustomization>) => {
    setCustomization(prev => ({
      ...prev,
      parts: {
        ...prev.parts,
        [partName]: {
          ...prev.parts[partName],
          ...updates
        }
      }
    }));
  }, []);

  // Generate CSS from the customization state - memoized to prevent infinite loops
  const generateCss = useMemo(() => {
    let css = '';

    // Loop through each part and generate CSS
    Object.entries(customization.parts).forEach(([partName, partStyles]) => {
      if (Object.keys(partStyles).length === 0) return;

      css += `::part(${partName}) {\n`;

      // Add each style property
      if (partStyles.backgroundColor) css += `  background-color: ${partStyles.backgroundColor};\n`;
      if (partStyles.textColor) css += `  color: ${partStyles.textColor};\n`;
      if (partStyles.borderRadius) css += `  border-radius: ${partStyles.borderRadius}px;\n`;
      if (partStyles.borderColor) css += `  border-color: ${partStyles.borderColor};\n`;
      if (partStyles.borderWidth) css += `  border-width: ${partStyles.borderWidth}px;\n`;
      if (partStyles.padding) css += `  padding: ${partStyles.padding}px;\n`;
      if (partStyles.fontWeight) css += `  font-weight: ${partStyles.fontWeight};\n`;
      if (partStyles.fontSize) css += `  font-size: ${partStyles.fontSize}px;\n`;
      if (partStyles.fontFamily) css += `  font-family: ${partStyles.fontFamily};\n`;
      if (partStyles.maxWidth) css += `  max-width: ${partStyles.maxWidth}px;\n`;

      // Add shadow if enabled
      if (partStyles.shadowEffect) {
        const intensity = partStyles.shadowIntensity || 0.1;
        css += `  box-shadow: 0 4px 6px rgba(0, 0, 0, ${intensity});\n`;
        // Add dark mode variant
        css += `  .dark & { box-shadow: 0 4px 6px rgba(0, 0, 0, ${intensity * 2}); }\n`;
      }

      css += `}\n\n`;

      // Add hover styles for buttons
      if (['start-button', 'end-button', 'send-button', 'minimize-button'].includes(partName)) {
        if (partStyles.hoverBackgroundColor || partStyles.hoverTextColor) {
          css += `::part(${partName}):hover {\n`;
          if (partStyles.hoverBackgroundColor) css += `  background-color: ${partStyles.hoverBackgroundColor};\n`;
          if (partStyles.hoverTextColor) css += `  color: ${partStyles.hoverTextColor};\n`;
          css += `}\n\n`;
        }
      }
    });

    return css;
  }, [customization.parts]);

  // Update the parent component when customization changes, but only after initial parsing
  useEffect(() => {
    if (initialParsingDoneRef.current) {
      onChangeRef.current(generateCss);
    }
  }, [generateCss]);

  // Parse existing CSS when the component mounts or when value changes significantly
  useEffect(() => {
    // Always parse on mount
    if (!initialParsingDoneRef.current) {
      // First time parsing
    }
    // Skip if we're just seeing our own generated CSS to prevent loops
    else if (generateCss.trim() === value.trim()) {
      return;
    }

    if (!value) {
      initialParsingDoneRef.current = true;
      return;
    }

    try {
      // Basic CSS parser
      const newState = { ...defaultCustomizationState };

      // First pass: parse regular styles
      const regularPartRegex = /::part\(([^)]+)\)\s*{([^}]*)}/g;
      let match;

      while ((match = regularPartRegex.exec(value)) !== null) {
        const partName = match[1];
        const cssBlock = match[2];

        // Skip hover states in this pass
        if (match[0].includes(':hover')) continue;

        if (newState.parts[partName as keyof typeof newState.parts]) {
          const part = { ...newState.parts[partName as keyof typeof newState.parts] };

          // Extract properties
          if (cssBlock.includes('background-color:')) {
            const bgMatch = cssBlock.match(/background-color:\s*([^;]+);/);
            if (bgMatch) {
              const color = bgMatch[1].trim();
              // Store the color value as is, whether it's hex, rgb, rgba, etc.
              part.backgroundColor = color;
            }
          }

          if (cssBlock.includes('color:') && !cssBlock.includes('background-color:')) {
            const colorMatch = cssBlock.match(/color:\s*([^;]+);/);
            if (colorMatch) {
              const color = colorMatch[1].trim();
              // Store the color value as is, whether it's hex, rgb, rgba, etc.
              part.textColor = color;
            }
          }

          if (cssBlock.includes('border-radius:')) {
            const radiusMatch = cssBlock.match(/border-radius:\s*([0-9]+)px/);
            if (radiusMatch) part.borderRadius = parseInt(radiusMatch[1]);
          }

          if (cssBlock.includes('font-size:')) {
            const fontSizeMatch = cssBlock.match(/font-size:\s*([0-9]+)px/);
            if (fontSizeMatch) part.fontSize = parseInt(fontSizeMatch[1]);
          }

          if (cssBlock.includes('font-family:')) {
            const fontFamilyMatch = cssBlock.match(/font-family:\s*([^;]+);/);
            if (fontFamilyMatch) part.fontFamily = fontFamilyMatch[1].trim();
          }

          if (cssBlock.includes('box-shadow:')) {
            part.shadowEffect = true;
            const shadowMatch = cssBlock.match(/box-shadow:[^(]*\([^,]+,[^,]+,[^,]+,\s*([0-9.]+)\)/);
            if (shadowMatch) part.shadowIntensity = parseFloat(shadowMatch[1]);
          }

          newState.parts[partName as keyof typeof newState.parts] = part;
        }
      }

      // Second pass: parse hover states
      const hoverPartRegex = /::part\(([^)]+)\):hover\s*{([^}]*)}/g;
      let hoverMatch;

      while ((hoverMatch = hoverPartRegex.exec(value)) !== null) {
        const partName = hoverMatch[1];
        const cssBlock = hoverMatch[2];

        if (newState.parts[partName as keyof typeof newState.parts]) {
          const part = { ...newState.parts[partName as keyof typeof newState.parts] };

          // Extract hover properties
          if (cssBlock.includes('background-color:')) {
            const bgMatch = cssBlock.match(/background-color:\s*([^;]+);/);
            if (bgMatch) {
              part.hoverBackgroundColor = bgMatch[1].trim();
            }
          }

          if (cssBlock.includes('color:')) {
            const colorMatch = cssBlock.match(/color:\s*([^;]+);/);
            if (colorMatch) {
              part.hoverTextColor = colorMatch[1].trim();
            }
          }

          newState.parts[partName as keyof typeof newState.parts] = part;
        }
      }

      // Only update state if we found some styles to apply
      if (Object.values(newState.parts).some(part => Object.keys(part).length > 0)) {
        setCustomization(newState);
      }

      // Mark initial parsing as done
      initialParsingDoneRef.current = true;
    } catch (error) {
      console.error('Error parsing CSS:', error);
      initialParsingDoneRef.current = true;
    }
  }, [value, generateCss]);

  // Render the customization controls for a specific part
  const renderPartControls = (partName: keyof CssCustomizationState['parts'], partLabel: string) => {
    const part = customization.parts[partName];
    const isButton = ['start-button', 'end-button', 'send-button', 'minimize-button'].includes(partName);
    const isText = ['status-text', 'message-text'].includes(partName);
    const isMessage = ['user-message', 'ai-message'].includes(partName);

    return (
      <AccordionItem value={partName}>
        <AccordionTrigger>{partLabel}</AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4 p-2">
            {/* Background Color */}
            <div className="grid grid-cols-2 items-center gap-2">
              <Label htmlFor={`${partName}-bg-color`}>Background Color</Label>
              <div className="flex gap-2">
                <Input
                  id={`${partName}-bg-color`}
                  type="color"
                  value={part.backgroundColor && part.backgroundColor.startsWith('#') ? part.backgroundColor : '#ffffff'}
                  onChange={e => {
                    // Update both the color picker and text input
                    updatePart(partName, { backgroundColor: e.target.value });
                  }}
                  className="w-12 p-1 h-10"
                />
                <Input
                  value={part.backgroundColor || ''}
                  onChange={e => {
                    // Allow any CSS color value in the text input
                    updatePart(partName, { backgroundColor: e.target.value });
                  }}
                  placeholder="transparent"
                />
              </div>
            </div>

            {/* Text Color */}
            <div className="grid grid-cols-2 items-center gap-2">
              <Label htmlFor={`${partName}-text-color`}>Text Color</Label>
              <div className="flex gap-2">
                <Input
                  id={`${partName}-text-color`}
                  type="color"
                  value={part.textColor && part.textColor.startsWith('#') ? part.textColor : '#000000'}
                  onChange={e => {
                    // Update both the color picker and text input
                    updatePart(partName, { textColor: e.target.value });
                  }}
                  className="w-12 p-1 h-10"
                />
                <Input
                  value={part.textColor || ''}
                  onChange={e => {
                    // Allow any CSS color value in the text input
                    updatePart(partName, { textColor: e.target.value });
                  }}
                  placeholder="inherit"
                />
              </div>
            </div>

            {/* Border Radius */}
            <div className="grid grid-cols-2 items-center gap-2">
              <Label htmlFor={`${partName}-border-radius`}>Border Radius</Label>
              <div className="flex gap-2 items-center">
                <Slider
                  id={`${partName}-border-radius`}
                  min={0}
                  max={20}
                  step={1}
                  value={[part.borderRadius || 0]}
                  onValueChange={values => updatePart(partName, { borderRadius: values[0] })}
                  className="flex-1"
                />
                <span className="w-8 text-center">{part.borderRadius || 0}px</span>
              </div>
            </div>

            {/* Additional controls based on part type */}
            {isButton && (
              <>
                {/* Hover Background Color */}
                <div className="grid grid-cols-2 items-center gap-2">
                  <Label htmlFor={`${partName}-hover-bg-color`}>Hover Background</Label>
                  <div className="flex gap-2">
                    <Input
                      id={`${partName}-hover-bg-color`}
                      type="color"
                      value={part.hoverBackgroundColor && part.hoverBackgroundColor.startsWith('#') ? part.hoverBackgroundColor : '#f0f0f0'}
                      onChange={e => {
                        // Update both the color picker and text input
                        updatePart(partName, { hoverBackgroundColor: e.target.value });
                      }}
                      className="w-12 p-1 h-10"
                    />
                    <Input
                      value={part.hoverBackgroundColor || ''}
                      onChange={e => {
                        // Allow any CSS color value in the text input
                        updatePart(partName, { hoverBackgroundColor: e.target.value });
                      }}
                      placeholder="none"
                    />
                  </div>
                </div>
              </>
            )}

            {isText && (
              <>
                {/* Font Size */}
                <div className="grid grid-cols-2 items-center gap-2">
                  <Label htmlFor={`${partName}-font-size`}>Font Size</Label>
                  <div className="flex gap-2 items-center">
                    <Slider
                      id={`${partName}-font-size`}
                      min={10}
                      max={24}
                      step={1}
                      value={[part.fontSize || 14]}
                      onValueChange={values => updatePart(partName, { fontSize: values[0] })}
                      className="flex-1"
                    />
                    <span className="w-8 text-center">{part.fontSize || 14}px</span>
                  </div>
                </div>

                {/* Font Family */}
                <div className="grid grid-cols-2 items-center gap-2">
                  <Label htmlFor={`${partName}-font-family`}>Font Family</Label>
                  <Select
                    value={part.fontFamily || 'inherit'}
                    onValueChange={value => updatePart(partName, { fontFamily: value })}
                  >
                    <SelectTrigger id={`${partName}-font-family`}>
                      <SelectValue placeholder="Select font" />
                    </SelectTrigger>
                    <SelectContent>
                      {fontFamilyOptions.map(font => (
                        <SelectItem key={font.value} value={font.value}>
                          {font.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            {isMessage && (
              <>
                {/* Shadow Effect */}
                <div className="grid grid-cols-2 items-center gap-2">
                  <Label htmlFor={`${partName}-shadow`}>Shadow Effect</Label>
                  <Switch
                    id={`${partName}-shadow`}
                    checked={part.shadowEffect || false}
                    onCheckedChange={checked => updatePart(partName, { shadowEffect: checked })}
                  />
                </div>

                {part.shadowEffect && (
                  <div className="grid grid-cols-2 items-center gap-2">
                    <Label htmlFor={`${partName}-shadow-intensity`}>Shadow Intensity</Label>
                    <div className="flex gap-2 items-center">
                      <Slider
                        id={`${partName}-shadow-intensity`}
                        min={0.05}
                        max={0.5}
                        step={0.05}
                        value={[part.shadowIntensity || 0.1]}
                        onValueChange={values => updatePart(partName, { shadowIntensity: values[0] })}
                        className="flex-1"
                      />
                      <span className="w-12 text-center">{part.shadowIntensity || 0.1}</span>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>
    );
  };

  return (
    <div className="space-y-4">
      <Accordion type="single" collapsible className="w-full">
        {renderPartControls('widget-container', 'Widget Container')}
        {renderPartControls('widget', 'Widget Body')}
        {renderPartControls('start-button', 'Start Button')}
        {renderPartControls('end-button', 'End Button')}
        {renderPartControls('avatar-container', 'Avatar Container')}
        {renderPartControls('status-text', 'Status Text')}
        {renderPartControls('minimize-button', 'Minimize Button')}
        {renderPartControls('message-container', 'Message Container')}
        {renderPartControls('user-message', 'User Message Bubbles')}
        {renderPartControls('ai-message', 'AI Message Bubbles')}
        {renderPartControls('message-text', 'Message Text')}
        {renderPartControls('input-container', 'Input Container')}
        {renderPartControls('input-field', 'Input Field')}
        {renderPartControls('send-button', 'Send Button')}
      </Accordion>
    </div>
  );
}
