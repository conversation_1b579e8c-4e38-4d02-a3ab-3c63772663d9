/**
 * This script helps reset and apply a fresh migration
 * Run with: node scripts/reset-migrations.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '..', 'supabase', 'migrations');
const BACKUP_DIR = path.join(__dirname, '..', 'supabase', 'migrations_backup');
const FRESH_MIGRATION = '20250501000000_fresh_schema.sql';

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Backup existing migrations
console.log('Backing up existing migrations...');
const migrationFiles = fs.readdirSync(MIGRATIONS_DIR)
  .filter(file => file.endsWith('.sql') && file !== FRESH_MIGRATION);

migrationFiles.forEach(file => {
  const sourcePath = path.join(MIGRATIONS_DIR, file);
  const destPath = path.join(BACKUP_DIR, file);
  fs.copyFileSync(sourcePath, destPath);
  console.log(`Backed up: ${file}`);
});

// Reset Supabase migrations
console.log('\nResetting Supabase migrations...');
try {
  // Keep only the fresh migration file
  migrationFiles.forEach(file => {
    const filePath = path.join(MIGRATIONS_DIR, file);
    fs.unlinkSync(filePath);
    console.log(`Removed: ${file}`);
  });

  console.log('\nApplying fresh migration...');
  console.log('To apply the migration, run:');
  console.log('supabase db reset');
  console.log('OR');
  console.log('supabase migration up');
  
  console.log('\nMigration reset complete!');
  console.log(`Your backup migrations are in: ${BACKUP_DIR}`);
  console.log(`Your fresh migration is: ${FRESH_MIGRATION}`);
} catch (error) {
  console.error('Error during migration reset:', error);
}
