'use client';

import { Check, Minus } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const features = [
  {
    name: "AI Agents",
    free: "1 agent",
    starter: "5 agents",
    professional: "Unlimited",
    enterprise: "Unlimited"
  },
  {
    name: "Monthly Call Minutes",
    free: "100 minutes",
    starter: "500 minutes",
    professional: "2,000 minutes",
    enterprise: "Custom"
  },
  {
    name: "Voice Options",
    free: "Standard voices",
    starter: "Premium voices",
    professional: "Custom voices",
    enterprise: "Custom brand voices"
  },
  {
    name: "Team Management",
    free: false,
    starter: true,
    professional: true,
    enterprise: true
  },
  {
    name: "Analytics",
    free: "Basic",
    starter: "Advanced",
    professional: "Advanced",
    enterprise: "Custom dashboards"
  },
  {
    name: "API Access",
    free: false,
    starter: false,
    professional: true,
    enterprise: true
  },
  {
    name: "Custom Integrations",
    free: false,
    starter: false,
    professional: false,
    enterprise: true
  },
  {
    name: "Support",
    free: "Email",
    starter: "Priority email",
    professional: "Phone & email",
    enterprise: "Dedicated manager"
  },
  {
    name: "SLA",
    free: false,
    starter: false,
    professional: false,
    enterprise: true
  }
];

export function PricingComparisonTable() {
  return (
    <div className="w-full overflow-auto max-w-6xl mx-auto">
      <Table>
        <TableCaption>A comparison of all available pricing plans</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Feature</TableHead>
            <TableHead>Free</TableHead>
            <TableHead>Starter</TableHead>
            <TableHead>Professional</TableHead>
            <TableHead>Enterprise</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {features.map((feature) => (
            <TableRow key={feature.name}>
              <TableCell className="font-medium">{feature.name}</TableCell>
              <TableCell>
                {typeof feature.free === 'boolean' ? (
                  feature.free ? <Check className="h-4 w-4 text-primary" /> : <Minus className="h-4 w-4 text-zinc-300 dark:text-zinc-600" />
                ) : (
                  feature.free
                )}
              </TableCell>
              <TableCell>
                {typeof feature.starter === 'boolean' ? (
                  feature.starter ? <Check className="h-4 w-4 text-primary" /> : <Minus className="h-4 w-4 text-zinc-300 dark:text-zinc-600" />
                ) : (
                  feature.starter
                )}
              </TableCell>
              <TableCell>
                {typeof feature.professional === 'boolean' ? (
                  feature.professional ? <Check className="h-4 w-4 text-primary" /> : <Minus className="h-4 w-4 text-zinc-300 dark:text-zinc-600" />
                ) : (
                  feature.professional
                )}
              </TableCell>
              <TableCell>
                {typeof feature.enterprise === 'boolean' ? (
                  feature.enterprise ? <Check className="h-4 w-4 text-primary" /> : <Minus className="h-4 w-4 text-zinc-300 dark:text-zinc-600" />
                ) : (
                  feature.enterprise
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
