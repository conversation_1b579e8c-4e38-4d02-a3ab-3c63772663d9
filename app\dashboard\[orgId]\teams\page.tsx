import { Suspense } from 'react'
import { TeamsTable } from '@/components/teams/teams-table'
import { Skeleton } from '@/components/ui/skeleton'
import { createClient } from '@/utils/supabase/server'
import { getOrganizationTeams } from '@/utils/supabase/queries'

interface PageProps {
  params: Promise<{
    orgId: string,
  }>
}

export default async function Page({ params }: PageProps) {
  const { orgId } = await params;
  const pageSize = 100; // Get more teams to display in the table

  const supabase = await createClient();
  const { data: teams, error } = await getOrganizationTeams(
    supabase,
    orgId,
    '',  // No search filter initially
    1,   // First page
    pageSize
  );

  if (error) {
    return <div className="p-4">Error fetching teams</div>
  }

  return (
    <div className="p-6">
      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <TeamsTable teams={teams || []} orgId={orgId} />
      </Suspense>
    </div>
  )
}