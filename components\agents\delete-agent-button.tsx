'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/Toasts/use-toast'
import { deleteAgent } from '@/actions/serverActions'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface DeleteAgentButtonProps {
  agentId: string
  orgId: string
  agentName: string
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
  onSuccess?: () => void
}

export function DeleteAgentButton({
  agentId,
  orgId,
  agentName,
  variant = 'destructive',
  size = 'default',
  className,
  onSuccess
}: DeleteAgentButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleDelete = async () => {
    setIsDeleting(true)

    try {
      const formData = new FormData()
      formData.append('agentId', agentId)
      formData.append('orgId', orgId)

      const initialState = { success: false, error: '', id: '' }
      const result = await deleteAgent(initialState, formData)

      if (result.success) {
        toast({
          title: 'Agent deleted',
          description: `${agentName} has been successfully deleted.`,
        })

        setIsOpen(false)

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess()
        } else {
          // Otherwise, redirect to the agents list
          router.push(`/dashboard/${orgId}/agents`)
          router.refresh()
        }
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to delete agent. Please try again.',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsOpen(true)}
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Delete
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete this agent?</DialogTitle>
            <DialogDescription>
              This will permanently delete <strong>{agentName}</strong> and all associated data.
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              onClick={(e: React.MouseEvent) => {
                e.preventDefault()
                handleDelete()
              }}
              disabled={isDeleting}
              variant="destructive"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
