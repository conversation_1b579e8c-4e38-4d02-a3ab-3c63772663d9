'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Copy } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/Toasts/use-toast'
import { duplicateAgent } from '@/actions/serverActions'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface DuplicateAgentButtonProps {
  agentId: string
  orgId: string
  agentName: string
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
  onSuccess?: (newAgentId: string) => void
}

export function DuplicateAgentButton({
  agentId,
  orgId,
  agentName,
  variant = 'outline',
  size = 'default',
  className,
  onSuccess
}: DuplicateAgentButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isDuplicating, setIsDuplicating] = useState(false)
  const [newName, setNewName] = useState(`${agentName} (Copy)`)
  const router = useRouter()
  const { toast } = useToast()

  const handleDuplicate = async () => {
    if (!newName.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a name for the new agent.',
        variant: 'destructive'
      })
      return
    }

    setIsDuplicating(true)

    try {
      const formData = new FormData()
      formData.append('agentId', agentId)
      formData.append('orgId', orgId)
      formData.append('newName', newName)

      const initialState = { success: false, error: '', id: '' }
      const result = await duplicateAgent(initialState, formData)

      if (result.success && result.id) {
        toast({
          title: 'Agent duplicated',
          description: `${agentName} has been successfully duplicated as ${newName}.`,
        })

        setIsOpen(false)

        // Always navigate to the edit page for the new agent
        // This avoids rendering issues in the agents table
        router.push(`/dashboard/${orgId}/agents/${result.id}`)

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess(result.id)
        }
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to duplicate agent. Please try again.',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error duplicating agent:', error)
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsDuplicating(false)
    }
  }

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsOpen(true)}
      >
        <Copy className="h-4 w-4 mr-2" />
        Duplicate
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Duplicate Agent</DialogTitle>
            <DialogDescription>
              Create a copy of <strong>{agentName}</strong> with a new name.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Label htmlFor="new-name">New Agent Name</Label>
            <Input
              id="new-name"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              placeholder="Enter a name for the new agent"
              className="mt-1"
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isDuplicating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDuplicate}
              disabled={isDuplicating || !newName.trim()}
            >
              {isDuplicating ? 'Duplicating...' : 'Duplicate'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
