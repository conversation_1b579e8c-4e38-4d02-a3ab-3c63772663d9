import { Suspense } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { ConversationsTable } from '@/components/conversations/conversations-table'
import { createClient } from '@/utils/supabase/server'
import { getOrganizationConversations } from '@/utils/supabase/queries'

interface PageProps {
  params: Promise<{ orgId: string }>
}

export default async function Page({ params }: PageProps) {
  const { orgId } = await params;
  const pageSize = 100; // Get more conversations to display in the table

  const supabase = await createClient();
  const { data: conversations, error } = await getOrganizationConversations(
    supabase,
    orgId,
    '',  // No search filter initially
    1,   // First page
    pageSize
  );

  if (error) {
    return <div className="p-4">Error fetching conversations</div>
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Conversation History</h1>
        <p className="text-muted-foreground">
          View and manage all conversations with your agents
        </p>
      </div>

      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <ConversationsTable
          conversations={conversations || []}
          orgId={orgId}
        />
      </Suspense>
    </div>
  )
}