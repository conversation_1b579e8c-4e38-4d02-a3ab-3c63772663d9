import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET /api/tenants
 * Get all tenants (admin only)
 */
export async function GET(request: Request) {
  try {
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is an admin (you may need to adjust this based on your admin role implementation)
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const status = searchParams.get('status') || 'active';

    // Calculate pagination
    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;

    // Build query
    let query = supabase
      .from('tenants')
      .select('*', { count: 'exact' });

    // Add status filter if provided
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    // Add search filter if provided
    if (search) {
      query = query.or(`name.ilike.%${search}%,slug.ilike.%${search}%,domain.ilike.%${search}%`);
    }

    // Add pagination
    query = query.range(start, end);

    // Execute query
    const { data, count, error } = await query;

    if (error) {
      console.error('Error fetching tenants:', error);
      return NextResponse.json(
        { error: 'Failed to fetch tenants' },
        { status: 500 }
      );
    }

    // Calculate if there are more pages
    const hasMore = count ? start + pageSize < count : false;

    return NextResponse.json({
      data,
      count: count || 0,
      hasMore,
      page
    });
  } catch (error) {
    console.error('Error in tenants route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tenants
 * Create a new tenant (admin only)
 */
export async function POST(request: Request) {
  try {
    const { name, slug, domain } = await request.json();

    // Validate required fields
    if (!name || !slug ) {
      return NextResponse.json(
        { error: 'Name, slug, and domain are required' },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Check if slug is available
    const { data: existingSlug } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingSlug) {
      return NextResponse.json(
        { error: 'Tenant slug already exists' },
        { status: 400 }
      );
    }

    // Check if domain is available
    const { data: existingDomain } = await supabase
      .from('tenants')
      .select('id')
      .eq('domain', domain)
      .single();

    if (existingDomain) {
      return NextResponse.json(
        { error: 'Domain already exists' },
        { status: 400 }
      );
    }

    // Create tenant
    const { data: tenantId, error } = await supabase
      .rpc('create_tenant', { tenant_name: name, tenant_slug: slug, tenant_domain: domain });

    if (error) {
      console.error('Error creating tenant:', error);
      return NextResponse.json(
        { error: 'Failed to create tenant' },
        { status: 500 }
      );
    }

    // Fetch the newly created tenant
    const { data: tenant } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', tenantId)
      .single();

    return NextResponse.json({
      success: true,
      data: tenant
    });
  } catch (error) {
    console.error('Error in tenants route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
