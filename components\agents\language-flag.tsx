'use client'

import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/utils/cn'

// Language data with ISO codes and flag emojis
export const languages = [
  { code: 'en', name: 'English', flag: '🇬🇧' },
  { code: 'es', name: 'Spanish', flag: '🇪🇸' },
  { code: 'fr', name: 'French', flag: '🇫🇷' },
  { code: 'de', name: 'German', flag: '🇩🇪' },
  { code: 'it', name: 'Italian', flag: '🇮🇹' },
  { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
  { code: 'ru', name: 'Russian', flag: '🇷🇺' },
  { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
  { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
  { code: 'ko', name: 'Korean', flag: '🇰🇷' },
  { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
  { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
  { code: 'bn', name: 'Bengali', flag: '🇧🇩' },
  { code: 'nl', name: 'Dutch', flag: '🇳🇱' },
  { code: 'tr', name: 'Turkish', flag: '🇹🇷' },
  { code: 'pl', name: 'Polish', flag: '🇵🇱' },
  { code: 'sv', name: 'Swedish', flag: '🇸🇪' },
]

export type LanguageCode = typeof languages[number]['code']

interface LanguageFlagProps {
  languageCode: LanguageCode
  size?: 'sm' | 'md' | 'lg'
  showTooltip?: boolean
  className?: string
}

export function LanguageFlag({
  languageCode,
  size = 'md',
  showTooltip = true,
  className
}: LanguageFlagProps) {
  const language = languages.find(lang => lang.code === languageCode)

  if (!language) return null

  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-10 w-10 text-base'
  }

  const Flag = () => (
    <div
      className={cn(
        "flex items-center justify-center rounded-full bg-muted border overflow-hidden",
        sizeClasses[size],
        className
      )}
    >
      <span role="img" aria-label={language.name} className="text-base leading-none">
        {language.flag}
      </span>
    </div>
  )

  if (!showTooltip) return <Flag />

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            <Flag />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{language.name}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

interface LanguageFlagsGroupProps {
  languageCodes: LanguageCode[]
  size?: 'sm' | 'md' | 'lg'
  maxVisible?: number
  className?: string
}

export function LanguageFlagsGroup({
  languageCodes,
  size = 'md',
  maxVisible = 3,
  className
}: LanguageFlagsGroupProps) {
  const visibleLanguages = languageCodes.slice(0, maxVisible)
  const remainingCount = languageCodes.length - maxVisible

  return (
    <div className={cn("flex items-center", className)}>
      <div className="flex -space-x-2">
        {visibleLanguages.map((code) => (
          <LanguageFlag key={code} languageCode={code} size={size} />
        ))}
      </div>

      {remainingCount > 0 && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className={cn(
                "flex items-center justify-center rounded-full bg-muted border ml-1 overflow-hidden",
                size === 'sm' ? 'h-6 w-6 text-xs' : size === 'md' ? 'h-8 w-8 text-sm' : 'h-10 w-10 text-base'
              )}>
                +{remainingCount}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                {languageCodes.slice(maxVisible).map((code) => {
                  const lang = languages.find(l => l.code === code)
                  return (
                    <p key={code} className="flex items-center gap-2">
                      <span>{lang?.flag}</span> {lang?.name}
                    </p>
                  )
                })}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  )
}
