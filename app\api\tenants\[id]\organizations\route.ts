import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET /api/tenants/[id]/organizations
 * Get all organizations for a tenant
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });
    
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    // Calculate pagination
    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;

    // Build query
    let query = supabase
      .from('organizations')
      .select('*', { count: 'exact' })
      .eq('tenant_id', id);

    // Add search filter if provided
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // Add pagination
    query = query.range(start, end);

    // Execute query
    const { data, count, error } = await query;

    if (error) {
      console.error('Error fetching organizations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch organizations' },
        { status: 500 }
      );
    }

    // Calculate if there are more pages
    const hasMore = count ? start + pageSize < count : false;

    return NextResponse.json({
      data,
      count: count || 0,
      hasMore,
      page
    });
  } catch (error) {
    console.error('Error in tenant organizations route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tenants/[id]/organizations
 * Create a new organization for a tenant
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { name } = await request.json();

    if (!name) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }
    
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });
    
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Create organization
    const { data: orgId, error } = await supabase
      .rpc('create_organization', { org_name: name, tenant_id: id });

    if (error) {
      console.error('Error creating organization:', error);
      return NextResponse.json(
        { error: 'Failed to create organization' },
        { status: 500 }
      );
    }

    // Fetch the newly created organization
    const { data: organization } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', orgId)
      .single();

    return NextResponse.json({
      success: true,
      data: organization
    });
  } catch (error) {
    console.error('Error in tenant organizations route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
