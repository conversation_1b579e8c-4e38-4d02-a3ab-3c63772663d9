'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/Toasts/use-toast';
import { Tenant } from '@/types/tenant';
import { User } from '@/types/user';
import { ArrowLeft, Loader2, UserPlus, Mail } from 'lucide-react';

interface UserWithOrganization extends User {
  organizations: {
    id: string;
    name: string;
  }[];
}

export default function TenantUsersPage() {
  const router = useRouter();
  const params = useParams();
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [users, setUsers] = useState<UserWithOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);

  // Use the id directly from params
  const id = params.id;

  // Fetch tenant and users on component mount
  useEffect(() => {
    fetchTenant();
    fetchTenantUsers();
    fetchAvailableUsers();
  }, [id]);

  // Fetch tenant details
  const fetchTenant = async () => {
    try {
      const response = await fetch(`/api/tenants/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch tenant');
      }
      const data = await response.json();
      setTenant(data.data);
    } catch (error) {
      console.error('Error fetching tenant:', error);
      toast({
        title: 'Error',
        description: 'Failed to load tenant details. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Fetch users for this tenant
  const fetchTenantUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/tenants/${id}/users`);
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const data = await response.json();
      setUsers(data.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load users. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch users not assigned to any tenant
  const fetchAvailableUsers = async () => {
    try {
      const response = await fetch('/api/users?unassigned=true');
      if (!response.ok) {
        throw new Error('Failed to fetch available users');
      }
      const data = await response.json();
      setAvailableUsers(data.data || []);
    } catch (error) {
      console.error('Error fetching available users:', error);
    }
  };

  // Open assign user dialog
  const openAssignDialog = () => {
    setSelectedUser(null);
    setDialogOpen(true);
  };

  // Handle user assignment
  const handleAssignUser = async () => {
    if (!selectedUser) {
      toast({
        title: 'Error',
        description: 'Please select a user to assign',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/tenants/${id}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: selectedUser,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to assign user');
      }

      toast({
        title: 'Success',
        description: 'User assigned to tenant successfully',
      });

      // Refresh user lists
      fetchTenantUsers();
      fetchAvailableUsers();
      setDialogOpen(false);
    } catch (error) {
      console.error('Error assigning user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  };

  // Remove user from tenant
  const handleRemoveUser = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this user from the tenant?')) {
      return;
    }

    try {
      const response = await fetch(`/api/tenants/${id}/users/${userId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to remove user');
      }

      toast({
        title: 'Success',
        description: 'User removed from tenant successfully',
      });

      // Refresh user lists
      fetchTenantUsers();
      fetchAvailableUsers();
    } catch (error) {
      console.error('Error removing user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  };

  // Go back to tenant page
  const goBack = () => {
    router.push(`/admin/tenants/${id}/organizations`);
  };

  return (
    <div className="container py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={goBack} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Organizations
        </Button>
        <h1 className="text-3xl font-bold">
          {tenant ? `${tenant.name} Users` : 'Users'}
        </h1>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <p className="text-muted-foreground">
            Manage users for this tenant
          </p>
          {tenant && (
            <p className="text-sm text-muted-foreground mt-1">
              Tenant ID: {id}
            </p>
          )}
        </div>
        <Button onClick={openAssignDialog}>
          <UserPlus className="mr-2 h-4 w-4" />
          Assign User
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            All users assigned to this tenant
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Organizations</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8">
                      No users found in this tenant.
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.full_name || 'N/A'}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {user.organizations?.length > 0
                          ? user.organizations.map(org => org.name).join(', ')
                          : 'None'}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.location.href = `mailto:${user.email}`}
                          >
                            <Mail className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRemoveUser(user.id)}
                          >
                            Remove
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Assign User Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Assign User to Tenant</DialogTitle>
            <DialogDescription>
              Assign an existing user to this tenant. They will be able to access this tenant and will get their own organization.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <label className="block text-sm font-medium mb-2">
              Select User
            </label>
            <Select onValueChange={setSelectedUser}>
              <SelectTrigger>
                <SelectValue placeholder="Select a user" />
              </SelectTrigger>
              <SelectContent>
                {availableUsers.length === 0 ? (
                  <SelectItem value="none" disabled>
                    No available users
                  </SelectItem>
                ) : (
                  availableUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.email} {user.full_name ? `(${user.full_name})` : ''}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignUser}
              disabled={!selectedUser || availableUsers.length === 0}
            >
              Assign User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

