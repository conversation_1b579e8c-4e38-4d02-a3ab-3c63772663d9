'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { WidgetSettings, defaultWidgetSettings } from './widget-context'

// Define the context type
interface GlobalWidgetContextType {
  globalWidgetSettings: WidgetSettings
  updateGlobalWidgetSettings: (settings: Partial<WidgetSettings>, agentId: string) => void
  updateGlobalCustomText: (key: keyof WidgetSettings['customText'], value: string, agentId: string) => void
  getSettingsForAgent: (agentId: string) => WidgetSettings
}

// Create the context with a default value
const GlobalWidgetContext = createContext<GlobalWidgetContextType | undefined>(undefined)

// Provider component
export function GlobalWidgetProvider({ 
  children,
}: { 
  children: ReactNode
}) {
  // Store settings for multiple agents
  const [agentSettings, setAgentSettings] = useState<Record<string, WidgetSettings>>({})
  
  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('globalWidgetSettings')
      if (savedSettings) {
        setAgentSettings(JSON.parse(savedSettings))
      }
    } catch (error) {
      console.error('Error loading widget settings from localStorage:', error)
    }
  }, [])
  
  // Save settings to localStorage whenever they change
  useEffect(() => {
    if (Object.keys(agentSettings).length > 0) {
      try {
        localStorage.setItem('globalWidgetSettings', JSON.stringify(agentSettings))
      } catch (error) {
        console.error('Error saving widget settings to localStorage:', error)
      }
    }
  }, [agentSettings])

  // Get settings for a specific agent
  const getSettingsForAgent = (agentId: string): WidgetSettings => {
    return agentSettings[agentId] || defaultWidgetSettings
  }

  // Update settings for a specific agent
  const updateGlobalWidgetSettings = (settings: Partial<WidgetSettings>, agentId: string) => {
    setAgentSettings(prev => {
      const currentSettings = prev[agentId] || defaultWidgetSettings
      const newSettings = { ...currentSettings, ...settings }
      
      // Dispatch an event to notify components that settings have changed
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('global-widget-settings-changed', {
          detail: {
            agentId,
            settings: newSettings,
            timestamp: Date.now()
          }
        }))
      }
      
      return {
        ...prev,
        [agentId]: newSettings
      }
    })
  }

  // Update custom text for a specific agent
  const updateGlobalCustomText = (key: keyof WidgetSettings['customText'], value: string, agentId: string) => {
    setAgentSettings(prev => {
      const currentSettings = prev[agentId] || defaultWidgetSettings
      const newSettings = {
        ...currentSettings,
        customText: {
          ...currentSettings.customText,
          [key]: value
        }
      }
      
      // Dispatch an event to notify components that settings have changed
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('global-widget-settings-changed', {
          detail: {
            agentId,
            settings: newSettings,
            timestamp: Date.now()
          }
        }))
      }
      
      return {
        ...prev,
        [agentId]: newSettings
      }
    })
  }

  return (
    <GlobalWidgetContext.Provider value={{ 
      globalWidgetSettings: defaultWidgetSettings, // Placeholder, use getSettingsForAgent instead
      updateGlobalWidgetSettings,
      updateGlobalCustomText,
      getSettingsForAgent
    }}>
      {children}
    </GlobalWidgetContext.Provider>
  )
}

// Custom hook to use the global widget context
export function useGlobalWidgetContext() {
  const context = useContext(GlobalWidgetContext)
  if (context === undefined) {
    throw new Error('useGlobalWidgetContext must be used within a GlobalWidgetProvider')
  }
  return context
}
