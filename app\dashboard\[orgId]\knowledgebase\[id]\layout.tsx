import { PropsWithChildren } from 'react'
import { cn } from "@/utils/cn"
import Link from 'next/link'
import { createClient } from '@/utils/supabase/server'
import { getOrganizationAgents } from '@/utils/supabase/queries'
import SearchList from '@/components/SearchList/search-list'
import { ListState, ListContext, BaseItem } from '@/types/list'
import { AgentItem } from '@/components/agent-list-components'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'

interface LayoutProps {
  params: Promise<{
    orgId: string,
    id: string
  }>,
  children: React.ReactNode
} 

export default async function Layout({ children, params }: LayoutProps) {
  const {orgId, id} = await params;
  const search = '';
  const page = 1;
  const pageSize = 10;

  const supabase = await createClient();
  const {data:agents, count, hasMore, error} = await getOrganizationAgents(
    supabase, 
    orgId,
    search,
    page,
    pageSize
  );

  const initialState: ListState<BaseItem> = {
    items: agents || [],
    count: count || 0,
    hasMore: hasMore || false,
    search,
    page
  }

  const listContext: ListContext = {
    orgId,
    basePath: `/dashboard/${orgId}/agents`,
    currentPath: `/dashboard/${orgId}/agents/${id}`,
    resourceType: 'agent'
  }

  return (
    <div className="@container/main grid grid-cols-12 h-full relative">
      {/* Left column: Agent list sidebar */}
      <div className={cn(
        "border-r overflow-y-auto",
        "transition-[grid-column] duration-300 ease-in-out",
        "hidden @xl/main:block @xl/main:col-span-3"
      )}>

         <div className="flex items-center justify-between p-4 space-y-4">
          <h2 className="text-lg font-semibold">Documents</h2>
          <Button size="sm" variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add 
          </Button>
        </div>


        <SearchList<BaseItem>
          context={listContext}
          fetchUrl={`/api/organizations/${orgId}/agents`}
          initialState={initialState}
          currentItemId={id}
          searchPlaceholder="Search agents..."
          listTitle="Agents"
          slots={{
            itemContent: AgentItem
          }}
        />
      </div>

      {/* Right column: Agent details */}
      <div className={cn(
        "overflow-y-auto",
        "transition-[grid-column] duration-300 ease-in-out",
        "col-span-12 @xl/main:col-span-9"
      )}>
        <div className="p-6">
          <div className="@xl/main:hidden mb-4">
            <Link 
              href={`/dashboard/${orgId}/agents`}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              ← Back to Agents
            </Link>
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}