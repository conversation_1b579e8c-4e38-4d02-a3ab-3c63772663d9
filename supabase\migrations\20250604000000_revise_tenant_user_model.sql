-- Revert the tenant_id column from users table since users can belong to multiple tenants
ALTER TABLE "public"."users" 
DROP COLUMN IF EXISTS "tenant_id";

-- Create a tenant_users junction table to track which users belong to which tenants
CREATE TABLE IF NOT EXISTS "public"."tenant_users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "tenant_id" "uuid" REFERENCES "public"."tenants"("id") ON DELETE CASCADE,
    "user_id" "uuid" REFERENCES "public"."users"("id") ON DELETE CASCADE,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "is_primary" boolean DEFAULT false,
    CONSTRAINT "tenant_users_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "tenant_users_tenant_user_unique" UNIQUE ("tenant_id", "user_id")
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS "idx_tenant_users_tenant_id" ON "public"."tenant_users" ("tenant_id");
CREATE INDEX IF NOT EXISTS "idx_tenant_users_user_id" ON "public"."tenant_users" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_tenant_users_is_primary" ON "public"."tenant_users" ("is_primary");

-- Update the handle_new_user function to work with the new model
CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
declare
  new_org_id uuid := gen_random_uuid();
  tenant_id_var uuid;
begin
  -- Try to get tenant_id from the request cookie
  -- This will be set by the middleware when a user signs up through a tenant subdomain
  BEGIN
    tenant_id_var := nullif(current_setting('request.cookie.tenant_id', true), '')::uuid;
  EXCEPTION
    WHEN OTHERS THEN
      tenant_id_var := NULL;
  END;
  
  -- Insert user
  insert into public.users (id, email, full_name, avatar_url)
  values (new.id, new.email, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');

  -- If signing up through a tenant, associate user with that tenant
  if tenant_id_var is not null then
    -- Add user to tenant
    insert into public.tenant_users (tenant_id, user_id, is_primary)
    values (tenant_id_var, new.id, true);
    
    -- Create org for this user in the tenant
    insert into public.organizations (id, name, tenant_id)
    values (new_org_id, 'My Workspace', tenant_id_var);

    -- Add as admin
    insert into public.organization_memberships (organization_id, user_id, role)
    values (new_org_id, new.id, 'admin');

    -- Add wallet
    insert into public.credit_wallets (organization_id, balance_cents)
    values (new_org_id, 0);
  else
    -- For users not created through a tenant, create an organization without tenant_id
    insert into public.organizations (id, name)
    values (new_org_id, 'My Workspace');

    -- Add as admin
    insert into public.organization_memberships (organization_id, user_id, role)
    values (new_org_id, new.id, 'admin');

    -- Add wallet
    insert into public.credit_wallets (organization_id, balance_cents)
    values (new_org_id, 0);
  end if;

  return new;
end;
$$;

-- Create function to add user to tenant and create organization
CREATE OR REPLACE FUNCTION "public"."add_user_to_tenant"("user_id" uuid, "tenant_id" uuid, "is_primary" boolean DEFAULT false) RETURNS uuid
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  new_org_id uuid := gen_random_uuid();
  existing_org_id uuid;
BEGIN
  -- Check if user already exists in this tenant
  IF EXISTS (
    SELECT 1
    FROM public.tenant_users
    WHERE tenant_users.tenant_id = add_user_to_tenant.tenant_id
    AND tenant_users.user_id = add_user_to_tenant.user_id
  ) THEN
    -- User already exists in this tenant, check if they have an organization
    SELECT o.id INTO existing_org_id
    FROM public.organizations o
    JOIN public.organization_memberships om ON o.id = om.organization_id
    WHERE om.user_id = add_user_to_tenant.user_id
    AND o.tenant_id = add_user_to_tenant.tenant_id
    LIMIT 1;
    
    IF existing_org_id IS NOT NULL THEN
      -- User already has an organization in this tenant
      RETURN existing_org_id;
    END IF;
  ELSE
    -- Add user to tenant
    INSERT INTO public.tenant_users (tenant_id, user_id, is_primary)
    VALUES (add_user_to_tenant.tenant_id, add_user_to_tenant.user_id, add_user_to_tenant.is_primary);
  END IF;
  
  -- Create a new organization for this user in the tenant
  INSERT INTO public.organizations (id, name, tenant_id)
  VALUES (new_org_id, 'My Workspace', add_user_to_tenant.tenant_id);
  
  -- Add user as admin
  INSERT INTO public.organization_memberships (organization_id, user_id, role)
  VALUES (new_org_id, add_user_to_tenant.user_id, 'admin');
  
  -- Add wallet
  INSERT INTO public.credit_wallets (organization_id, balance_cents)
  VALUES (new_org_id, 0);
  
  RETURN new_org_id;
END;
$$;

-- Update the is_tenant_admin function to work with the new model
CREATE OR REPLACE FUNCTION "public"."is_tenant_admin"("tenant_id" uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.tenant_admins
    WHERE tenant_admins.tenant_id = $1
    AND tenant_admins.user_id = auth.uid()
  );
END;
$$;

-- Create function to check if user belongs to tenant
CREATE OR REPLACE FUNCTION "public"."is_tenant_user"("tenant_id" uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.tenant_users
    WHERE tenant_users.tenant_id = $1
    AND tenant_users.user_id = auth.uid()
  );
END;
$$;

-- Create RLS policies for tenant_users table
ALTER TABLE "public"."tenant_users" ENABLE ROW LEVEL SECURITY;

-- Tenant admins can view users in their tenant
CREATE POLICY "Tenant admins can view users in their tenant" ON "public"."tenant_users" 
FOR SELECT USING (
  "public"."is_tenant_admin"(tenant_id)
);

-- System admins can manage all tenant users
CREATE POLICY "System admins can manage all tenant users" ON "public"."tenant_users" 
FOR ALL USING (
  "public"."is_admin"()
);
