'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Clipboard, Check } from 'lucide-react'
import { useWidgetStore } from '@/stores/widget-store'

interface EmbedCodeModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  agentId: string
}

export function EmbedCodeModal({ open, onOpenChange, agentId }: EmbedCodeModalProps) {
  const [copied, setCopied] = useState(false)
  const { getSettingsForAgent } = useWidgetStore()
  const settings = getSettingsForAgent(agentId)

  const copyEmbedCode = () => {
    try {
      const embedCode = `<div id="widget-container"></div>
<script src="${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js"></script>
<script>
  ConversationWidget({
    agentId: '${agentId}',
    position: '${settings.position || 'bottom-right'}',
    containerId: 'widget-container',
    type: '${settings.widgetType || 'standalone'}',
    customParts: {
      container: 'widget-container',
      startButton: 'start-button',
      endButton: 'end-button',
      avatarContainer: 'avatar-container',
      statusText: 'status-text',
      ${settings.hideIcon ? 'hideIcon: true,' : ''}
      ${settings.customIcon ? `customIcon: \`${settings.customIcon}\`,` : ''}
      customText: ${JSON.stringify(settings.customText || {}, null, 2)}
    }
  });
</script>
${settings.customCSS ? `<style>\n${settings.customCSS}\n</style>` : ''}`

      navigator.clipboard.writeText(embedCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Error copying embed code:', error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Embed Code</DialogTitle>
          <DialogDescription>
            Copy this code to embed the widget on your website
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <div className="bg-muted p-4 rounded-lg text-sm font-mono overflow-x-auto whitespace-pre-wrap max-h-[400px] overflow-y-auto">
{`<div id="widget-container"></div>

<script src="${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js"></script>
<script>
  ConversationWidget({
    agentId: '${agentId}',
    position: '${settings.position || 'bottom-right'}',
    containerId: 'widget-container',
    type: '${settings.widgetType || 'standalone'}',
    customParts: {
      container: 'widget-container',
      startButton: 'start-button',
      endButton: 'end-button',
      avatarContainer: 'avatar-container',
      statusText: 'status-text',
      ${settings.hideIcon ? 'hideIcon: true,' : ''}
      ${settings.customIcon ? `customIcon: \`${settings.customIcon}\`,` : ''}
      customText: ${JSON.stringify(settings.customText || {}, null, 2)}
    }
  });
</script>
${settings.customCSS ? `<style>\n${settings.customCSS}\n</style>` : ''}`}
          </div>
        </div>

        <DialogFooter>
          <Button onClick={copyEmbedCode} className="flex items-center gap-2">
            {copied ? <Check className="h-4 w-4" /> : <Clipboard className="h-4 w-4" />}
            {copied ? 'Copied!' : 'Copy Code'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
