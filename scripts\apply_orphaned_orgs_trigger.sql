-- Function to delete orphaned organizations
CREATE OR REPLACE FUNCTION public.delete_orphaned_organization()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  org_id UUID := OLD.organization_id;
  member_count INT;
BEGIN
  -- Check if the organization has any remaining members
  SELECT COUNT(*) INTO member_count
  FROM organization_memberships
  WHERE organization_id = org_id;

  -- If no members remain, delete the organization and all related data
  IF member_count = 0 THEN
    -- Log the deletion (optional, for debugging)
    RAISE NOTICE 'Deleting orphaned organization: %', org_id;

    -- The organization and all related data will be automatically deleted
    -- due to the CASCADE constraints on foreign keys
    DELETE FROM organizations WHERE id = org_id;
  END IF;

  RETURN OLD;
END;
$$;

-- Create a trigger to run after a membership is deleted
DROP TRIGGER IF EXISTS check_orphaned_organization ON organization_memberships;
CREATE TRIGGER check_orphaned_organization
AFTER DELETE ON organization_memberships
FOR EACH ROW
EXECUTE FUNCTION delete_orphaned_organization();

-- Helper function to check if a function exists (for testing)
CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM pg_proc 
    JOIN pg_namespace ON pg_proc.pronamespace = pg_namespace.oid
    WHERE pg_proc.proname = function_name
    AND pg_namespace.nspname = 'public'
  );
$$;

-- Add comments to explain the purpose of these functions
COMMENT ON FUNCTION public.delete_orphaned_organization() IS 'Automatically deletes organizations that have no remaining members';
COMMENT ON FUNCTION public.function_exists() IS 'Helper function to check if a function exists';
