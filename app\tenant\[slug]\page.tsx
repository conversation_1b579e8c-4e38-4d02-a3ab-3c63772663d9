import { getTenantContext, getTenantOrganizations } from '@/utils/tenant/context';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';

interface TenantPageProps {
  params: Promise<{ slug: string }>;
}

export default async function TenantPage({ params }: TenantPageProps) {
  const { slug } = await params;
  const { tenant } = await getTenantContext(slug);
  const cookieStore = await cookies();

  if (!tenant) {
    return redirect('/');
  }
  
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return redirect(`/tenant/${slug}/signin`);
  }
  
  // Get organizations for this tenant that the user is a member of
  const { data: memberships } = await supabase
    .from('organization_memberships')
    .select(`
      organization:organization_id (
        id,
        name,
        tenant_id
      )
    `)
    .eq('user_id', user.id);
  
  // Filter organizations that belong to this tenant
  const tenantOrgs = memberships
    ?.filter(m => m.organization?.tenant_id === tenant.id)
    .map(m => m.organization) || [];
  
  // If user has no organizations in this tenant, create one
  if (tenantOrgs.length === 0) {
    const { data: orgId, error } = await supabase
      .rpc('create_organization', { 
        org_name: `${user.email?.split('@')[0]}'s Workspace`,
        tenant_id: tenant.id
      });
    
    if (!error && orgId) {
      // Set the last organization ID cookie
      cookieStore.set('last_org_id', orgId, { path: '/' });
      return redirect(`/tenant/${slug}/dashboard/${orgId}`);
    }
  } else {
    // Use the first organization
    const firstOrg = tenantOrgs[0];
    if (firstOrg) {
      // Set the last organization ID cookie
      cookieStore.set('last_org_id', firstOrg.id, { path: '/' });
      return redirect(`/tenant/${slug}/dashboard/${firstOrg.id}`);
    }
  }
  
  // Fallback - should not reach here
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">Welcome to {tenant.name}</h1>
      <p className="text-gray-600 mb-8">Setting up your workspace...</p>
    </div>
  );
}
