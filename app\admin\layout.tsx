import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { Toaster } from '@/components/ui/Toasts/toaster';
import { Suspense } from 'react';
import Link from 'next/link';
import { 
  LayoutDashboard, 
  Users, 
  Building2, 
  Settings, 
  LogOut 
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'Admin Dashboard',
  description: 'Superadmin dashboard for managing tenants and system settings',
};

async function checkAdminAccess() {
  const supabase = await createClient();
  
  // Check if user is authenticated
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return false;
  }
  
  // Check if user is an admin
  const { data: isAdmin, error: adminError } = await supabase.rpc('is_admin');
  if (adminError || !isAdmin) {
    return false;
  }
  
  return true;
}

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const isAdmin = await checkAdminAccess();
  
  if (!isAdmin) {
    return redirect('/');
  }
  
  return (
    <div className="flex min-h-screen bg-gray-100 dark:bg-zinc-900">
      {/* Sidebar */}
      <div className="w-64 bg-white dark:bg-zinc-800 shadow-md">
        <div className="p-6">
          <h1 className="text-xl font-bold">Admin Dashboard</h1>
        </div>
        <nav className="mt-6">
          <ul>
            <li>
              <Link 
                href="/admin" 
                className="flex items-center px-6 py-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
              >
                <LayoutDashboard className="h-5 w-5 mr-3" />
                Dashboard
              </Link>
            </li>
            <li>
              <Link 
                href="/admin/tenants" 
                className="flex items-center px-6 py-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
              >
                <Building2 className="h-5 w-5 mr-3" />
                Tenants
              </Link>
            </li>
            <li>
              <Link 
                href="/admin/users" 
                className="flex items-center px-6 py-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
              >
                <Users className="h-5 w-5 mr-3" />
                Users
              </Link>
            </li>
            <li>
              <Link 
                href="/admin/settings" 
                className="flex items-center px-6 py-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
              >
                <Settings className="h-5 w-5 mr-3" />
                Settings
              </Link>
            </li>
            <li className="mt-auto">
              <Link 
                href="/signout" 
                className="flex items-center px-6 py-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
              >
                <LogOut className="h-5 w-5 mr-3" />
                Sign Out
              </Link>
            </li>
          </ul>
        </nav>
      </div>
      
      {/* Main content */}
      <div className="flex-1">
        <main className="p-6">
          {children}
        </main>
      </div>
      
      <Suspense>
        <Toaster />
      </Suspense>
    </div>
  );
}
