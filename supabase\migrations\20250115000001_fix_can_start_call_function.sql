-- Fix the can_start_call function to use the new unified agents table structure
-- This function now gets the ElevenLabs agent ID from agents.external_id instead of agent_configs.external_provider_id

CREATE OR REPLACE FUNCTION public.can_start_call(agent uuid, estimated_cost integer)
 RETURNS call_check_result
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$DECLARE
  org_id UUID;
  agent_team_id UUID;
  agent_type TEXT;
  org_balance INT;
  agent_daily INT;
  agent_limit INT;
  team_daily INT;
  team_limit INT;
  elevenlabs_agent_id TEXT;
  result call_check_result;
BEGIN
  -- Initialize result with default values
  result.status := 'error';
  result.message := NULL;
  result.elevenlabs_agent_id := NULL;

  -- Get agent organization, team, type, and external_id
  SELECT organization_id, team_id, type, external_id 
  INTO org_id, agent_team_id, agent_type, elevenlabs_agent_id
  FROM agents 
  WHERE id = agent;

  -- Check if agent exists
  IF org_id IS NULL THEN
    result.message := 'Agent not found';
    RETURN result;
  END IF;

  -- Check if this is an ElevenLabs agent and has an external_id
  IF agent_type != 'elevenlabs' OR elevenlabs_agent_id IS NULL THEN
    result.message := 'ElevenLabs agent ID not found for this agent';
    RETURN result;
  END IF;

  -- Check organization balance
  SELECT balance_cents INTO org_balance 
  FROM credit_wallets 
  WHERE organization_id = org_id;

  IF org_balance < estimated_cost THEN 
    result.message := 'Insufficient organization credits';
    RETURN result;
  END IF;

  -- Check agent daily limit
  SELECT daily_limit_cents INTO agent_limit 
  FROM agent_budget_limits 
  WHERE agent_id = agent;

  IF agent_limit IS NOT NULL THEN
    SELECT COALESCE(SUM(-amount_cents), 0) INTO agent_daily
    FROM credit_transactions 
    WHERE agent_id = agent 
      AND created_at >= date_trunc('day', now());

    IF agent_daily + estimated_cost > agent_limit THEN 
      result.message := 'Agent daily budget limit reached';
      RETURN result;
    END IF;
  END IF;

  -- Check team daily limit (if agent belongs to a team)
  IF agent_team_id IS NOT NULL THEN
    SELECT daily_limit_cents INTO team_limit 
    FROM team_budget_limits 
    WHERE team_id = agent_team_id;

    IF team_limit IS NOT NULL THEN
      SELECT COALESCE(SUM(-amount_cents), 0) INTO team_daily
      FROM credit_transactions 
      WHERE team_id = agent_team_id 
        AND created_at >= date_trunc('day', now());

      IF team_daily + estimated_cost > team_limit THEN 
        result.message := 'Team budget limit reached';
        RETURN result;
      END IF;
    END IF;
  END IF;

  -- All checks passed, return success with the ElevenLabs agent ID
  result.status := 'success';
  result.elevenlabs_agent_id := elevenlabs_agent_id;
  RETURN result;
END;$function$;

-- Grant permissions
GRANT ALL ON FUNCTION public.can_start_call(agent uuid, estimated_cost integer) TO anon;
GRANT ALL ON FUNCTION public.can_start_call(agent uuid, estimated_cost integer) TO authenticated;
GRANT ALL ON FUNCTION public.can_start_call(agent uuid, estimated_cost integer) TO service_role;
