export interface BaseItem {
id: string
name: string
[key: string]: any
}

export interface ListItem{
    item: BaseItem
    isActive: boolean
    context: ListContext 
}

export interface ListResponse<T> {
  data: T[]
  count: number
  hasMore: boolean
  error?: any
  page: number
}

export interface ListContext {
  orgId: string
  basePath: string
  currentPath: string
  resourceType: string
}

export interface ListSlots<T> {
  header?: React.ReactNode
  beforeSearch?: React.ReactNode
  afterSearch?: React.ReactNode
  beforeList?: React.ReactNode
  afterList?: React.ReactNode
  footer?: React.ReactNode
  itemContent?: React.ComponentType<{ 
    item: T
    isActive: boolean 
    context: ListContext
  }>
  emptyState?: React.ReactNode
}

export interface ListState<T> {
  items: T[]
  count: number
  hasMore: boolean
  search?: string
  page?: number
}

export interface GenericListProps<T extends BaseItem>{
  // Required props
  context: ListContext
  fetchUrl: string
  initialState: ListState<T>
  
  // Optional props
  currentItemId?: string
  pageSize?: number
  searchPlaceholder?: string
  listTitle?: string
  slots?: ListSlots<T>
}