import { Metadata } from 'next';
import { Toaster } from '@/components/ui/Toasts/toaster';
import { PropsWithChildren, Suspense } from 'react';
import { getURL } from '@/utils/helpers';
import { getTenantContext, addUserToTenant, checkUserTenantAccess } from '@/utils/tenant/context';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { cookies } from 'next/headers';
import 'styles/main.css';

interface TenantLayoutProps {
  params: Promise<{ slug: string }>;
  children: React.ReactNode;
}

export async function generateMetadata({ params }: TenantLayoutProps): Promise<Metadata> {
  const { slug } = await params;
  const { tenant } = await getTenantContext(slug);

  if (!tenant) {
    return {
      title: 'Tenant Not Found',
      description: 'The requested tenant does not exist or is inactive.'
    };
  }

  return {
    metadataBase: new URL(getURL()),
    title: tenant.name,
    description: `${tenant.name} - Powered by BotCom AI`,
    openGraph: {
      title: tenant.name,
      description: `${tenant.name} - Powered by BotCom AI`
    }
  };
}

export default async function TenantLayout({ params, children }: TenantLayoutProps) {
  try {
    console.log('TenantLayout: Starting');
    const { slug } = await params;
    console.log(`TenantLayout: Processing slug: ${slug}`);

    const { tenant, isAdmin } = await getTenantContext(slug);

    // If tenant doesn't exist, redirect to main site
    if (!tenant) {
      console.log(`TenantLayout: No tenant found for slug: ${slug}, redirecting to root`);

      // Get the root domain from environment variables
      const rootDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
        .replace(/^https?:\/\//, '')  // Remove protocol if present
        .replace(/\/+$/, '');         // Remove trailing slashes

      // Construct the full URL with protocol
      const redirectUrl = `https://${rootDomain}/`;
      console.log(`TenantLayout: Redirecting to: ${redirectUrl}`);

      return redirect('/');
    }

    console.log(`TenantLayout: Found tenant: ${tenant.name}`);

    // Get user authentication status
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    const cookieStore = await cookies();

    console.log(`TenantLayout: User authenticated: ${!!user}`);


  // If not authenticated, redirect to tenant login page
  if (!user) {
    // Store the tenant slug in a cookie for post-login redirect
    cookieStore.set('tenant_slug', slug, { path: '/' });
    return redirect(`/tenant/${slug}/signin`);
  }

  // Check if user has access to this tenant
  const hasAccess = await checkUserTenantAccess(user.id, tenant.id);

  // If user doesn't have access to this tenant yet, add them
  if (!hasAccess) {
    try {
      // Add user to this tenant and create an organization
      // Set isPrimary to true if this is their first tenant
      const { data: tenantUsers } = await supabase
        .from('tenant_users')
        .select('id')
        .eq('user_id', user.id);

      const isPrimary = !tenantUsers || tenantUsers.length === 0;

      // Add user to tenant and create an organization
      const orgId = await addUserToTenant(user.id, tenant.id, isPrimary);

      // Set the last organization ID cookie
      cookieStore.set('last_org_id', orgId, { path: '/' });
    } catch (error) {
      console.error('Error adding user to tenant:', error);
    }
  }

  // Store the tenant context in a cookie for client components
  cookieStore.set('tenant_context', JSON.stringify({
    id: tenant.id,
    name: tenant.name,
    slug: tenant.slug,
    isAdmin
  }), { path: '/' });

  return (
    <>
      <main
        id="skip"
        className="min-h-[calc(100dvh-4rem)] md:min-h[calc(100dvh-5rem)]"
      >
        {children}
      </main>
      <Suspense>
        <Toaster />
      </Suspense>
    </>
  );
  } catch (error) {
    console.error('Error in TenantLayout:', error);
    // In case of any error, redirect to the main site
    return redirect('/');
  }
}
