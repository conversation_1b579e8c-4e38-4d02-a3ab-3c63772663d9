import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { getUser, getUserDetails } from '@/utils/supabase/queries'
import { UserSettingsForm } from '@/components/users/user-settings-form'
import { UserSecurityForm } from '@/components/users/user-security-form'
import { UserPreferencesForm } from '@/components/users/user-preferences-form'

interface PageProps {
  params: Promise<{ orgId: string }>
}

export default async function UserSettingsPage({ params }: PageProps) {
  const { orgId } = await params
  
  // Get current user
  const supabase = await createClient()
  const user = await getUser(supabase)
  
  if (!user) {
    return redirect('/signin')
  }
  
  // Get user details
  const userDetails = await getUserDetails(supabase)
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">User Settings</h1>
          <p className="text-muted-foreground">Manage your account settings and preferences</p>
        </div>
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your profile information and email address
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserSettingsForm 
                user={{
                  id: user.id,
                  email: user.email ?? '',
                  full_name: userDetails?.full_name ?? '',
                  avatar_url: userDetails?.avatar_url ?? null,
                  created_at: user.created_at,
                  updated_at: user.updated_at
                }}
                orgId={orgId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your password and security preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserSecurityForm 
                userId={user.id}
                orgId={orgId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Preferences</CardTitle>
              <CardDescription>
                Customize your experience and notification settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserPreferencesForm 
                userId={user.id}
                orgId={orgId}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
