import { Metadata } from 'next';
import Footer from '@/components/ui/Footer';
import Navbar from '@/components/ui/Navbar';
import { Toaster } from '@/components/ui/Toasts/toaster';
import { PropsWithChildren, Suspense } from 'react';
import { getURL } from '@/utils/helpers';
import 'styles/main.css';

const title = 'BotCom AI';
const description = 'AI-powered conversational agents for your business.';

export const metadata: Metadata = {
  metadataBase: new URL(getURL()),
  title: title,
  description: description,
  openGraph: {
    title: title,
    description: description
  }
};

export default async function Layout({ children }: PropsWithChildren) {
  return (
    <>

    <Navbar />
    <main
      id="skip"
      className="min-h-[calc(100dvh-4rem)] md:min-h[calc(100dvh-5rem)]"
    >
      {children}
    </main>
    <Footer />
    </>


  );
}
