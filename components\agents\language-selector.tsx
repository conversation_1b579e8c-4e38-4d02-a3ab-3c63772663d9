'use client'

import { useState, useEffect } from 'react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList
} from '@/components/ui/command'
import { CustomCommandItem } from './custom-command-item'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Check, ChevronsUpDown, X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/utils/cn'
import { languagesWithSvgFlags, LanguageFlagSvg } from './svg-flags'

interface LanguageSelectorProps {
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
}

export function LanguageSelector({
  value,
  onValueChange,
  placeholder = "Select language",
  disabled = false
}: LanguageSelectorProps) {
  const [open, setOpen] = useState(false)
  const [selectedLanguage, setSelectedLanguage] = useState<typeof languagesWithSvgFlags[number] | undefined>()

  // Update selected language when value changes
  useEffect(() => {
    const language = languagesWithSvgFlags.find(lang => lang.code === value)
    setSelectedLanguage(language)
  }, [value])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedLanguage ? (
            <div className="flex items-center gap-2">
              <LanguageFlagSvg languageCode={selectedLanguage.code} size="sm" />
              <span>{selectedLanguage.name}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search language..." />
          <CommandEmpty>No language found.</CommandEmpty>
          <CommandList className="max-h-[300px]">
            <CommandGroup>
              {languagesWithSvgFlags.map((language) => (
                <CustomCommandItem
                  key={language.code}
                  value={language.code}
                  onSelect={() => {
                    onValueChange(language.code)
                    setOpen(false)
                  }}
                  onItemClick={() => {
                    onValueChange(language.code)
                    setOpen(false)
                  }}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <LanguageFlagSvg languageCode={language.code} size="sm" />
                  <span>{language.name}</span>
                  {/* <span className="text-xs text-muted-foreground ml-2">
                    ({language.region})
                  </span> */}
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      value === language.code ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CustomCommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

interface MultiLanguageSelectorProps {
  values: string[]
  onValuesChange: (values: string[]) => void
  placeholder?: string
  disabled?: boolean
  maxSelections?: number
}

export function MultiLanguageSelector({
  values,
  onValuesChange,
  placeholder = "Select languages",
  disabled = false,
  maxSelections = 10
}: MultiLanguageSelectorProps) {
  const [open, setOpen] = useState(false)
  const [selectedLanguages, setSelectedLanguages] = useState<typeof languagesWithSvgFlags[number][]>([])

  // Update selected languages when values change
  useEffect(() => {
    const languages = languagesWithSvgFlags.filter(lang => values.includes(lang.code))
    setSelectedLanguages(languages)
  }, [values])

  const handleSelect = (code: string) => {
    if (values.includes(code)) {
      onValuesChange(values.filter(v => v !== code))
    } else {
      if (values.length < maxSelections) {
        onValuesChange([...values, code])
      }
    }
  }

  const removeLanguage = (code: string) => {
    onValuesChange(values.filter(v => v !== code))
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            <span className={cn(
              values.length === 0 && "text-muted-foreground"
            )}>
              {values.length > 0
                ? `${values.length} language${values.length > 1 ? 's' : ''} selected`
                : placeholder}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <Command>
            <CommandInput placeholder="Search language..." />
            <CommandEmpty>No language found.</CommandEmpty>
            <CommandList className="max-h-[300px]">
              <CommandGroup>
                {languagesWithSvgFlags.map((language) => (
                  <CustomCommandItem
                    key={language.code}
                    value={language.code}
                    onSelect={() => handleSelect(language.code)}
                    onItemClick={() => handleSelect(language.code)}
                    className="flex items-center gap-2 cursor-pointer"
                    disabled={values.length >= maxSelections && !values.includes(language.code)}
                  >
                    <LanguageFlagSvg languageCode={language.code} size="sm" />
                    <span>{language.name}</span>
                    <span className="text-xs text-muted-foreground ml-auto">
                      {language.region}
                    </span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        values.includes(language.code) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CustomCommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedLanguages.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedLanguages.map(language => (
            <Badge
              key={language.code}
              variant="secondary"
              className="flex items-center gap-1 py-1 pl-1 pr-2"
            >
              <LanguageFlagSvg languageCode={language.code} size="sm" />
              <span>{language.name}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1"
                onClick={() => removeLanguage(language.code)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove {language.name}</span>
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
