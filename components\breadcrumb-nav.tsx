"use client"

import * as React from "react"
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useEntityName } from "@/hooks/use-entity-name"

// Map route segments to display names
const segmentNames: Record<string, string> = {
  'dashboard': 'Dashboard',
  'agents': 'Agents',
  'teams': 'Teams',
  'conversations': 'Conversations',
  'settings': 'Settings',
  'users': 'Users',
  'knowledgebase': 'Knowledge Base',
}

export function BreadcrumbNav() {
  const pathname = usePathname()
  const { entityName, isLoading } = useEntityName()

  // Skip rendering breadcrumbs if we're at the root
  if (pathname === '/') {
    return <h1 className="text-base font-medium">BotCom AI</h1>
  }

  // Split the pathname into segments
  const segments = pathname.split('/').filter(Boolean)

  // Don't show breadcrumbs for simple paths or just show the app name
  if (segments.length <= 1 || (segments.length === 2 && segments[0] === 'dashboard')) {
    return <h1 className="text-base font-medium">BotCom AI</h1>
  }

  // Create breadcrumb items
  const breadcrumbs = segments.map((segment, index) => {
    // Skip 'dashboard' and organization ID in the breadcrumb display
    if (index === 0 && segment === 'dashboard') {
      return null
    }
    if (index === 1 && segments[0] === 'dashboard') {
      return null
    }

    // Create the href for this breadcrumb
    const href = '/' + segments.slice(0, index + 1).join('/')

    // Get display name for the segment
    let displayName = segmentNames[segment] || segment

    // For the last segment, if it looks like an ID, use the fetched entity name
    if (index === segments.length - 1 && (segment.length > 20 || /^[a-f0-9-]+$/i.test(segment))) {
      // If we're loading or don't have an entity name yet, show a loading state or fallback
      if (isLoading) {
        displayName = '...' // Loading indicator
      } else if (entityName) {
        displayName = entityName
      } else {
        displayName = 'Details' // Fallback
      }
    }

    // Last item is the current page
    const isLastItem = index === segments.length - 1

    return (
      <React.Fragment key={segment}>
        <BreadcrumbItem>
          {isLastItem ? (
            <BreadcrumbPage>{displayName}</BreadcrumbPage>
          ) : (
            <BreadcrumbLink asChild>
              <Link href={href}>{displayName}</Link>
            </BreadcrumbLink>
          )}
        </BreadcrumbItem>
        {!isLastItem && <BreadcrumbSeparator />}
      </React.Fragment>
    )
  }).filter(Boolean) // Remove null items

  // If no breadcrumbs after filtering, just show the app name
  if (breadcrumbs.length === 0) {
    return <h1 className="text-base font-medium">BotCom AI</h1>
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
