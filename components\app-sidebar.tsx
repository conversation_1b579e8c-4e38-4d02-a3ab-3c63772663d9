"use client"

import * as React from "react"
import {
  ArrowUpCircleIcon,
  BarChartIcon,
  CameraIcon,
  ClipboardListIcon,
  DatabaseIcon,
  FileCodeIcon,
  FileIcon,
  FileTextIcon,
  FolderIcon,
  HelpCircleIcon,
  LayoutDashboardIcon,
  ListIcon,
  SearchIcon,
  SettingsIcon,
  UsersIcon,
  HistoryIcon,
  BotIcon,
  LibraryBigIcon,
  User,
} from "lucide-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {OrganizationSelector} from "@/components/organization-selector"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const getNavData = (orgId?: string, tenantSlug?: string) => {
  // Base path depends on whether we're in tenant mode or not
  const basePath = tenantSlug ? `/tenant/${tenantSlug}/dashboard/${orgId}` : `/dashboard/${orgId}`;

  return {
    navMain: [
      {
        title: "Dashboard",
        url: basePath,
        icon: LayoutDashboardIcon,
      },
      {
        title: "Teams",
        url: `${basePath}/teams`,
        icon: BotIcon,
      },
      {
        title: "Agents",
        url: `${basePath}/agents`,
        icon: BotIcon,
      },
      {
        title: "Call History",
        url: `${basePath}/conversations`,
        icon: HistoryIcon,
      },
      // {
      //   title: "Knowledge Base",
      //   url: `${basePath}/knowledge-base`,
      //   icon: LibraryBigIcon,
      // },
      {
        title: "Users",
        url: `${basePath}/users`,
        icon: UsersIcon,
      },
    ],
  navClouds: [
    {
      title: "Capture",
      icon: CameraIcon,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: FileTextIcon,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: FileCodeIcon,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: SettingsIcon,
    },
    {
      title: "Get Help",
      url: "#",
      icon: HelpCircleIcon,
    },
    {
      title: "Search",
      url: "#",
      icon: SearchIcon,
    },
  ],
  documents: [
    {
      name: "Data Library",
      url: "#",
      icon: DatabaseIcon,
    },
    {
      name: "Reports",
      url: "#",
      icon: ClipboardListIcon,
    },
    {
      name: "Word Assistant",
      url: "#",
      icon: FileIcon,
    },
  ],
  };
};

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user: any;
  organizations: any;
  currentOrganization: string;
  tenantSlug?: string;
}



export function AppSidebar({ user, organizations, currentOrganization, tenantSlug, ...props }: AppSidebarProps) {

  const data = getNavData(currentOrganization, tenantSlug)
  // console.log('here.',organizations)
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>

        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <ArrowUpCircleIcon className="h-5 w-5" />
                <span className="text-base font-semibold">BotCom</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>

        </SidebarMenu>
        <OrganizationSelector organizations={organizations} currentOrganization={currentOrganization} />

      </SidebarHeader>
      <SidebarContent>

        <NavMain items={data.navMain} />
        {/* <NavDocuments items={data.documents} /> */}
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>

        <NavUser user={user} currentOrganization={currentOrganization} />
      </SidebarFooter>
    </Sidebar>
  )
}
