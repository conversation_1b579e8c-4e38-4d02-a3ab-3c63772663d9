"use client"

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { createClient } from '@/utils/supabase/client'

// This hook fetches entity names based on the current URL path
export function useEntityName() {
  const pathname = usePathname()
  const [entityName, setEntityName] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    // Parse the pathname to determine what type of entity we need to fetch
    const segments = pathname.split('/').filter(Boolean)
    
    // We need at least 3 segments (dashboard/orgId/entityType/entityId)
    if (segments.length < 4 || segments[0] !== 'dashboard') {
      setEntityName(null)
      return
    }

    const entityType = segments[2] // agents, teams, users, etc.
    const entityId = segments[3]
    
    // Skip if we don't have an ID that looks like a UUID
    if (!entityId || !entityId.match(/^[0-9a-f-]+$/i)) {
      setEntityName(null)
      return
    }

    const fetchEntityName = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const supabase = createClient()
        
        // Different queries based on entity type
        switch (entityType) {
          case 'agents': {
            const { data, error } = await supabase
              .from('agents')
              .select('name')
              .eq('id', entityId)
              .single()
              
            if (error) throw error
            setEntityName(data?.name || 'Agent')
            break
          }
          
          case 'teams': {
            const { data, error } = await supabase
              .from('agent_teams')
              .select('name')
              .eq('id', entityId)
              .single()
              
            if (error) throw error
            setEntityName(data?.name || 'Team')
            break
          }
          
          case 'users': {
            const { data, error } = await supabase
              .from('organization_memberships')
              .select(`
                user:user_id (full_name, email)
              `)
              .eq('user_id', entityId)
              .single()
              
            if (error) throw error
            setEntityName(data?.user?.full_name || data?.user?.email || 'User')
            break
          }
          
          case 'conversations': {
            const { data, error } = await supabase
              .from('conversations')
              .select('id, agent:agent_id(name)')
              .eq('id', entityId)
              .single()
              
            if (error) throw error
            setEntityName(data?.agent?.name ? `Conversation with ${data.agent.name}` : 'Conversation')
            break
          }
          
          default:
            setEntityName('Details')
        }
      } catch (err) {
        console.error('Error fetching entity name:', err)
        setError(err instanceof Error ? err : new Error('Unknown error'))
        setEntityName('Details') // Fallback
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchEntityName()
  }, [pathname])
  
  return { entityName, isLoading, error }
}
