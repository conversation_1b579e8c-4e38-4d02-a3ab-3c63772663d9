import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/middleware';

// Function to extract tenant slug from hostname
export function getTenantFromHostname(hostname: string): string | null {
  console.log(`Extracting tenant from hostname: ${hostname}`);

  // Skip for localhost, IP addresses, or Vercel preview deployments
  if (
    hostname === 'localhost' ||
    /^127\.0\.0\.1/.test(hostname) ||
    /^\d+\.\d+\.\d+\.\d+$/.test(hostname) ||
    hostname.endsWith('.vercel.app')
  ) {
    console.log(`Development/preview hostname detected: ${hostname}, skipping tenant routing`);
    return null;
  }

  // Get the production domain from environment variables
  // Ensure we're using just the domain without protocol or trailing slash
  const productionDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
    .replace(/^https?:\/\//, '')  // Remove protocol if present
    .replace(/\/+$/, '');         // Remove trailing slashes

  console.log(`Production domain: ${productionDomain}`);

  // Check if this is the root domain itself or www subdomain
  if (hostname === productionDomain || hostname === `www.${productionDomain}`) {
    console.log(`Root domain or www subdomain detected: ${hostname}`);
    return null;
  }

  // Check if this is a custom domain (not a subdomain of the production domain)
  if (!hostname.endsWith(`.${productionDomain}`)) {
    console.log(`Custom domain detected: ${hostname}`);
    // This could be a custom domain - we'll need to look it up in the database
    return `custom:${hostname}`;
  }

  // Extract subdomain from hostname
  const parts = hostname.split('.');
  console.log(`Hostname parts: ${JSON.stringify(parts)}`);

  if (parts.length > 2) {
    // This is a subdomain
    console.log(`Subdomain detected: ${parts[0]}`);
    return parts[0];
  }

  // This is the root domain
  console.log(`Root domain detected: ${hostname}`);
  return null;
}

// Function to handle tenant routing
export async function handleTenantRouting(request: NextRequest): Promise<NextResponse | null> {
  const { pathname, hostname } = new URL(request.url);
  console.log(`handleTenantRouting for: ${hostname}${pathname}`);

  // Get the production domain from environment variables
  const productionDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
    .replace(/^https?:\/\//, '')  // Remove protocol if present
    .replace(/\/+$/, '');         // Remove trailing slashes

  // Special case: if this is the root domain or www subdomain, skip tenant routing
  if (hostname === productionDomain || hostname === `www.${productionDomain}`) {
    console.log(`Root domain access detected, skipping tenant routing`);
    return null;
  }

  const tenantSlug = getTenantFromHostname(hostname);
  console.log(`Tenant slug: ${tenantSlug}`);

  // Skip tenant routing for API routes, static files, etc.
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname === '/favicon.ico'
  ) {
    console.log(`Skipping tenant routing for system path: ${pathname}`);
    return null;
  }

  // If no tenant slug found, this is the main app
  if (!tenantSlug) {
    console.log(`No tenant slug found, treating as main app`);
    // If trying to access tenant-specific routes from main domain, redirect to home
    if (pathname.startsWith('/tenant')) {
      console.log(`Redirecting tenant-specific route from main domain: ${pathname}`);
      const redirectUrl = new URL('/', request.url);
      console.log(`Redirecting to: ${redirectUrl.toString()}`);
      return NextResponse.redirect(redirectUrl);
    }
    return null;
  }

  // Handle custom domain lookup
  if (tenantSlug.startsWith('custom:')) {
    const customDomain = tenantSlug.replace('custom:', '');
    console.log(`Looking up custom domain: ${customDomain}`);

    try {
      const { supabase } = createClient(request);

      // Look up the tenant by custom domain
      const { data: tenant, error } = await supabase
        .from('tenants')
        .select('slug')
        .eq('domain', customDomain)
        .eq('status', 'active')
        .single();

      if (error) {
        console.log(`Error looking up tenant by domain: ${error.message}`);

        // Check if this is a "no rows returned" error, which means no tenants exist with this domain
        if (error.code === 'PGRST116') {
          console.log(`No tenant found with domain: ${customDomain}`);
        }
      }

      if (!tenant) {
        // Custom domain not found or inactive
        // Ensure we're using a proper URL with protocol
        const rootDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
          .replace(/^https?:\/\//, '')  // Remove protocol if present
          .replace(/\/+$/, '');         // Remove trailing slashes

        // Make sure we have a full URL with protocol
        const redirectUrl = `https://${rootDomain}/`;
        console.log(`No tenant found for custom domain. Redirecting to: ${redirectUrl}`);
        return NextResponse.redirect(redirectUrl);
      }

      // Rewrite the request to use the tenant slug
      console.log(`Rewriting custom domain URL for tenant: ${tenant.slug}, pathname: ${pathname}`);
      const rewriteUrl = new URL(`/tenant/${tenant.slug}${pathname}`, request.url);
      console.log(`Rewriting to: ${rewriteUrl.toString()}`);
      return NextResponse.rewrite(rewriteUrl);
    } catch (error) {
      console.error(`Unexpected error in custom domain lookup:`, error);

      // In case of any error, redirect to the main domain
      const rootDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
        .replace(/^https?:\/\//, '')  // Remove protocol if present
        .replace(/\/+$/, '');         // Remove trailing slashes

      const redirectUrl = `https://${rootDomain}/`;
      console.log(`Error occurred. Redirecting to: ${redirectUrl}`);
      return NextResponse.redirect(redirectUrl);
    }
  }

  // For tenant subdomains, rewrite the request to the /tenant/[slug] path
  // and add the tenant_id to the request headers for auth context
  console.log(`Processing subdomain tenant: ${tenantSlug}, pathname: ${pathname}`);

  try {
    // First, verify that this tenant exists in the database
    const { supabase } = createClient(request);

    console.log(`Verifying tenant exists: ${tenantSlug}`);
    const { data: tenantExists, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .eq('status', 'active')
      .single();

    if (tenantError) {
      console.log(`Error looking up tenant by slug: ${tenantError.message}`);

      // Check if this is a "no rows returned" error
      if (tenantError.code === 'PGRST116') {
        console.log(`No tenant found with slug: ${tenantSlug}`);
      }
    }

    if (!tenantExists) {
      // Tenant not found or inactive, redirect to main domain
      const rootDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
        .replace(/^https?:\/\//, '')  // Remove protocol if present
        .replace(/\/+$/, '');         // Remove trailing slashes

      const redirectUrl = `https://${rootDomain}/`;
      console.log(`No tenant found for slug. Redirecting to: ${redirectUrl}`);
      return NextResponse.redirect(redirectUrl);
    }

    // Tenant exists, proceed with rewrite
    console.log(`Tenant found, rewriting URL for: ${tenantSlug}, pathname: ${pathname}`);
    const rewriteUrl = new URL(`/tenant/${tenantSlug}${pathname}`, request.url);
    console.log(`Rewriting to: ${rewriteUrl.toString()}`);
    const response = NextResponse.rewrite(rewriteUrl);

    // Store tenant slug in a header for auth context
    response.headers.set('x-tenant-slug', tenantSlug);

    // If this is a signup or signin page, we'll need to set a cookie with the tenant ID
    // so the handle_new_user trigger can associate the user with this tenant
    if (pathname.includes('/signin') || pathname.includes('/signup')) {
      // We already have the tenant ID from the verification above
      console.log(`Setting tenant_id cookie for auth: ${tenantExists.id}`);

      // Set a cookie with the tenant ID that will be available during auth
      response.cookies.set('tenant_id', tenantExists.id, {
        path: '/',
        httpOnly: true,
        sameSite: 'lax'
      });
    }

    return response;
  } catch (error) {
    console.error(`Unexpected error in subdomain tenant processing:`, error);

    // In case of any error, redirect to the main domain
    const rootDomain = (process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'botcom.net')
      .replace(/^https?:\/\//, '')  // Remove protocol if present
      .replace(/\/+$/, '');         // Remove trailing slashes

    const redirectUrl = `https://${rootDomain}/`;
    console.log(`Error occurred. Redirecting to: ${redirectUrl}`);
    return NextResponse.redirect(redirectUrl);
  }
}
