'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { generateSignedUrl } from '@/utils/elevenlabs/client';
import { toast } from 'sonner';

interface StartCallButtonProps {
  agentId: string;
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  estimatedCost?: number;
  onCallStarted?: () => void;
  onCallFailed?: (error: Error) => void;
}

/**
 * A button component that generates a signed URL and starts a call with an ElevenLabs agent
 */
export function StartCallButton({
  agentId,
  className,
  variant = 'default',
  size = 'default',
  estimatedCost,
  onCallStarted,
  onCallFailed
}: StartCallButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    setIsLoading(true);
    
    try {
      // Generate a signed URL
      const signedUrl = await generateSignedUrl(agentId, estimatedCost);
      
      // Here you would typically use the signed URL to establish a connection
      // with the ElevenLabs service. For this example, we'll just open the URL in a new tab.
      window.open(signedUrl, '_blank');
      
      toast.success('Call started successfully');
      
      if (onCallStarted) {
        onCallStarted();
      }
    } catch (error) {
      console.error('Error starting call:', error);
      
      toast.error(error instanceof Error ? error.message : 'Failed to start call');
      
      if (onCallFailed && error instanceof Error) {
        onCallFailed(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      className={className}
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={isLoading}
    >
      {isLoading ? 'Starting call...' : 'Start Call'}
    </Button>
  );
}

/**
 * A more advanced component that handles the entire call flow
 */
export function ElevenLabsCallButton({
  agentId,
  className,
  variant = 'default',
  size = 'default',
  estimatedCost,
  onCallStarted,
  onCallFailed
}: StartCallButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [callStatus, setCallStatus] = useState<'idle' | 'connecting' | 'active' | 'ended'>('idle');

  const handleStartCall = async () => {
    if (callStatus !== 'idle') return;
    
    setIsLoading(true);
    setCallStatus('connecting');
    
    try {
      // Generate a signed URL
      const signedUrl = await generateSignedUrl(agentId, estimatedCost);
      
      // Here you would typically:
      // 1. Initialize the ElevenLabs client with the signed URL
      // 2. Set up event listeners for call status changes
      // 3. Start the call
      
      // For this example, we'll just simulate a successful connection
      setCallStatus('active');
      
      toast.success('Connected to agent');
      
      if (onCallStarted) {
        onCallStarted();
      }
    } catch (error) {
      console.error('Error starting call:', error);
      
      setCallStatus('idle');
      toast.error(error instanceof Error ? error.message : 'Failed to start call');
      
      if (onCallFailed && error instanceof Error) {
        onCallFailed(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleEndCall = () => {
    // Here you would typically:
    // 1. Disconnect from the ElevenLabs service
    // 2. Clean up any resources
    
    setCallStatus('ended');
    setTimeout(() => setCallStatus('idle'), 2000);
    toast.info('Call ended');
  };

  return (
    <Button
      className={className}
      variant={callStatus === 'active' ? 'destructive' : variant}
      size={size}
      onClick={callStatus === 'active' ? handleEndCall : handleStartCall}
      disabled={isLoading || callStatus === 'connecting' || callStatus === 'ended'}
    >
      {isLoading ? 'Connecting...' : 
       callStatus === 'active' ? 'End Call' :
       callStatus === 'ended' ? 'Call Ended' : 'Start Call'}
    </Button>
  );
}
