'use client'

import { useState, FormEvent } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/Toasts/use-toast'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

interface User {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  created_at?: string
  updated_at?: string
}

interface UserSettingsFormProps {
  user: User
  orgId: string
}

export function UserSettingsForm({ user, orgId }: UserSettingsFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isEmailSubmitting, setIsEmailSubmitting] = useState(false)
  const [fullName, setFullName] = useState(user.full_name || '')
  const [avatarUrl, setAvatarUrl] = useState(user.avatar_url || '')
  const [errors, setErrors] = useState({ fullName: '', avatarUrl: '' })

  const validateForm = () => {
    const newErrors = { fullName: '', avatarUrl: '' }
    let isValid = true

    if (fullName.length < 2) {
      newErrors.fullName = 'Name must be at least 2 characters.'
      isValid = false
    }

    if (avatarUrl && !/^https?:\/\/.+/.test(avatarUrl)) {
      newErrors.avatarUrl = 'Please enter a valid URL.'
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  async function handleSubmit(e: FormEvent) {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // In a real implementation, you would update the user profile
      // For now, we'll just show a success message
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      })

      // Refresh the page to show the updated data
      router.refresh()
    } catch (error) {
      console.error('Error updating profile:', error)
      toast({
        title: 'Error',
        description: 'Failed to update profile. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  async function handleEmailChange() {
    const emailInput = document.getElementById('email') as HTMLInputElement;
    const newEmail = emailInput?.value;
    
    if (!newEmail || newEmail === user.email) {
      toast({
        title: 'No changes',
        description: 'The email has not been changed.',
      });
      return;
    }
    
    // Validate email format
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!emailRegex.test(newEmail)) {
      toast({
        title: 'Invalid email',
        description: 'Please enter a valid email address.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsEmailSubmitting(true);
    
    try {
      // In a real implementation, you would update the user email
      // For now, we'll just show a success message
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Email update initiated',
        description: 'A verification email has been sent to the new address. Please check your inbox.',
      });
    } catch (error) {
      console.error('Error updating email:', error);
      toast({
        title: 'Error',
        description: 'Failed to update email. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsEmailSubmitting(false);
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Avatar className="h-16 w-16">
          <AvatarImage src={user.avatar_url || undefined} alt={user.full_name || ''} />
          <AvatarFallback className="text-lg">
            {user.full_name ? user.full_name.substring(0, 2).toUpperCase() : user.email.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className="text-lg font-medium">{user.full_name || user.email}</h3>
          <p className="text-sm text-muted-foreground">{user.email}</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Update your personal information</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input 
                id="fullName"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Enter full name" 
              />
              {errors.fullName && (
                <p className="text-sm font-medium text-red-500">{errors.fullName}</p>
              )}
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                This is the name that will be displayed to other users.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="avatarUrl">Avatar URL</Label>
              <Input 
                id="avatarUrl"
                value={avatarUrl}
                onChange={(e) => setAvatarUrl(e.target.value)}
                placeholder="https://example.com/avatar.jpg" 
              />
              {errors.avatarUrl && (
                <p className="text-sm font-medium text-red-500">{errors.avatarUrl}</p>
              )}
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                URL to your profile picture.
              </p>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Email Address</CardTitle>
          <CardDescription>Update your email address</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="flex items-center justify-between">
              <Input
                id="email"
                type="email"
                defaultValue={user.email}
                className="max-w-md mr-2"
                placeholder="<EMAIL>"
                name="newEmail"
              />
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleEmailChange}
                disabled={isEmailSubmitting}
              >
                {isEmailSubmitting ? 'Updating...' : 'Change Email'}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              A verification email will be sent to the new address. The change will take effect after verification.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
