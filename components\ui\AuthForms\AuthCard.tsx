'use client';

import { ReactNode } from 'react';
import {
  Card as Shadcn<PERSON><PERSON>,
  CardHeader,
  CardContent,
  CardTitle
} from '@/components/ui/card';

interface AuthCardProps {
  title: string;
  children: ReactNode;
}

export function AuthCard({ title, children }: AuthCardProps) {
  return (
    <ShadcnCard className="w-full">
      <CardHeader>
        <CardTitle className="text-center">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </ShadcnCard>
  );
}
