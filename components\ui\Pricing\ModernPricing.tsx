'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import type { Tables } from '@/types_db';
import { getStripe } from '@/utils/stripe/client';
import { checkoutWithStripe } from '@/utils/stripe/server';
import { getErrorRedirect } from '@/utils/helpers';
import { User } from '@supabase/supabase-js';
import { cn } from '@/utils/cn';
import { useRouter, usePathname } from 'next/navigation';
import { useState } from 'react';
import { Check, Sparkles } from 'lucide-react';
import { PricingFAQ } from './PricingFAQ';
import { PricingComparisonTable } from './PricingComparisonTable';
import { PricingTestimonials } from './PricingTestimonials';

type Subscription = Tables<'subscriptions'>;
type Product = Tables<'products'>;
type Price = Tables<'prices'>;
interface ProductWithPrices extends Product {
  prices: Price[];
}
interface PriceWithProduct extends Price {
  products: Product | null;
}
interface SubscriptionWithProduct extends Subscription {
  prices: PriceWithProduct | null;
}

interface Props {
  user: User | null | undefined;
  products: ProductWithPrices[];
  subscription: SubscriptionWithProduct | null;
}

type BillingInterval = 'lifetime' | 'year' | 'month';

// Sample features for each plan - you can customize these based on your actual product offerings
const planFeatures = {
  'Free': [
    '1 AI Agent',
    '100 minutes of calls per month',
    'Basic analytics',
    'Email support'
  ],
  'Starter': [
    '5 AI Agents',
    '500 minutes of calls per month',
    'Advanced analytics',
    'Team management',
    'Priority support'
  ],
  'Professional': [
    'Unlimited AI Agents',
    '2000 minutes of calls per month',
    'Custom voice options',
    'Advanced analytics & reporting',
    'Dedicated account manager',
    'API access'
  ],
  'Enterprise': [
    'Unlimited AI Agents',
    'Custom call volume',
    'Custom voice options',
    'Advanced analytics & reporting',
    'Dedicated account manager',
    'API access',
    'Custom integrations',
    'SLA guarantees'
  ]
};

// Default features if product name doesn't match any in planFeatures
const defaultFeatures = [
  'AI Agents',
  'Call minutes',
  'Basic analytics',
  'Email support'
];

export default function ModernPricing({ user, products, subscription }: Props) {
  const intervals = Array.from(
    new Set(
      products.flatMap((product) =>
        product?.prices?.map((price) => price?.interval)
      )
    )
  );
  const router = useRouter();
  const [billingInterval, setBillingInterval] =
    useState<BillingInterval>('month');
  const [priceIdLoading, setPriceIdLoading] = useState<string>();
  const currentPath = usePathname();

  const handleStripeCheckout = async (price: Price) => {
    setPriceIdLoading(price.id);

    if (!user) {
      setPriceIdLoading(undefined);
      return router.push('/signin/signup');
    }

    const { errorRedirect, sessionId } = await checkoutWithStripe(
      price,
      currentPath
    );

    if (errorRedirect) {
      setPriceIdLoading(undefined);
      return router.push(errorRedirect);
    }

    if (!sessionId) {
      setPriceIdLoading(undefined);
      return router.push(
        getErrorRedirect(
          currentPath,
          'An unknown error occurred.',
          'Please try again later or contact a system administrator.'
        )
      );
    }

    const stripe = await getStripe();
    stripe?.redirectToCheckout({ sessionId });

    setPriceIdLoading(undefined);
  };

  if (!products.length) {
    return (
      <section className="w-full py-12 md:py-24 lg:py-32 bg-zinc-50 dark:bg-zinc-900">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                No pricing plans found
              </h1>
              <p className="mx-auto max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
                Please check back later or contact our support team for assistance.
              </p>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                Admin: Create pricing plans in your{' '}
                <a
                  className="text-primary underline"
                  href="https://dashboard.stripe.com/products"
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  Stripe Dashboard
                </a>
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-zinc-50 dark:bg-zinc-900">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Simple, Transparent Pricing
            </h1>
            <p className="mx-auto max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
              Choose the perfect plan for your business needs. All plans include core features.
            </p>
          </div>

          {/* Billing interval toggle */}
          {intervals.length > 1 && (
            <div className="flex items-center space-x-4 mt-6">
              <div className="grid grid-cols-2 gap-2 rounded-lg bg-zinc-100 p-1 dark:bg-zinc-800">
                {intervals.includes('month') && (
                  <button
                    onClick={() => setBillingInterval('month')}
                    className={cn(
                      "px-3 py-1.5 text-sm font-medium rounded-md",
                      billingInterval === 'month'
                        ? "bg-white text-zinc-900 shadow-sm dark:bg-zinc-700 dark:text-zinc-50"
                        : "text-zinc-500 dark:text-zinc-400"
                    )}
                  >
                    Monthly
                  </button>
                )}
                {intervals.includes('year') && (
                  <button
                    onClick={() => setBillingInterval('year')}
                    className={cn(
                      "px-3 py-1.5 text-sm font-medium rounded-md",
                      billingInterval === 'year'
                        ? "bg-white text-zinc-900 shadow-sm dark:bg-zinc-700 dark:text-zinc-50"
                        : "text-zinc-500 dark:text-zinc-400"
                    )}
                  >
                    Yearly
                    <Badge variant="outline" className="ml-2 bg-primary/10 text-primary border-0">
                      Save 20%
                    </Badge>
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Pricing cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12 max-w-6xl mx-auto">
          {products.map((product) => {
            const price = product?.prices?.find(
              (price) => price.interval === billingInterval
            );
            if (!price) return null;

            const priceString = new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: price.currency!,
              minimumFractionDigits: 0
            }).format((price?.unit_amount || 0) / 100);

            const isPopular = product.name === 'Professional' || product.name === 'Starter';
            const features = planFeatures[product.name as keyof typeof planFeatures] || defaultFeatures;

            return (
              <Card
                key={product.id}
                className={cn(
                  "flex flex-col h-full",
                  isPopular && "border-primary shadow-md dark:border-primary"
                )}
              >
                {isPopular && (
                  <div className="px-3 py-1 text-xs text-center text-primary-foreground bg-primary rounded-t-lg">
                    Popular
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{product.name}</CardTitle>
                  <CardDescription>{product.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  <div className="flex items-baseline mb-5">
                    <span className="text-3xl font-bold">{priceString}</span>
                    <span className="ml-1 text-sm text-zinc-500 dark:text-zinc-400">/{billingInterval}</span>
                  </div>
                  <ul className="space-y-2 text-sm">
                    {features.map((feature, i) => (
                      <li key={i} className="flex items-center">
                        <Check className="mr-2 h-4 w-4 text-primary" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    variant={isPopular ? "default" : "outline"}
                    size="lg"
                    className="w-full"
                    disabled={priceIdLoading === price.id}
                    onClick={() => handleStripeCheckout(price)}
                  >
                    {subscription ? 'Manage' : 'Get Started'}
                  </Button>
                </CardFooter>
              </Card>
            );
          })}
        </div>

        {/* Comparison table */}
        <div className="mt-20">
          <h2 className="text-2xl font-bold text-center mb-8">Compare Plans</h2>
          <PricingComparisonTable />
        </div>

        {/* Enterprise section */}
        <div className="mt-20">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-8 md:p-12 dark:bg-zinc-800/50 max-w-6xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2 md:gap-12">
              <div className="flex flex-col justify-between space-y-4">
                <div>
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    <h3 className="text-xl font-bold">Enterprise Plan</h3>
                  </div>
                  <p className="mt-2 text-zinc-500 dark:text-zinc-400">
                    Need a custom solution for your business? Our enterprise plan offers tailored features, dedicated support, and flexible pricing.
                  </p>
                </div>
                <Button className="w-full md:w-auto" size="lg">
                  Contact Sales
                </Button>
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="flex items-start gap-2">
                  <Check className="mt-1 h-4 w-4 text-primary" />
                  <div>
                    <h4 className="font-medium">Custom Integration</h4>
                    <p className="text-sm text-zinc-500 dark:text-zinc-400">Connect with your existing systems</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="mt-1 h-4 w-4 text-primary" />
                  <div>
                    <h4 className="font-medium">Dedicated Support</h4>
                    <p className="text-sm text-zinc-500 dark:text-zinc-400">Priority access to our support team</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="mt-1 h-4 w-4 text-primary" />
                  <div>
                    <h4 className="font-medium">Custom Voice Models</h4>
                    <p className="text-sm text-zinc-500 dark:text-zinc-400">Create unique voices for your brand</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="mt-1 h-4 w-4 text-primary" />
                  <div>
                    <h4 className="font-medium">SLA Guarantees</h4>
                    <p className="text-sm text-zinc-500 dark:text-zinc-400">Guaranteed uptime and performance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonials section */}
        <div className="mt-20">
          <PricingTestimonials />
        </div>

        {/* FAQ section */}
        <div className="mt-20">
          <PricingFAQ />
        </div>

        {/* CTA section */}
        <div className="mt-20 text-center">
          <div className="max-w-2xl mx-auto px-4">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl mb-4">
              Ready to Transform Your Customer Interactions?
            </h2>
            <p className="text-zinc-500 dark:text-zinc-400 mb-8">
              Join thousands of businesses that are leveraging AI to provide exceptional customer experiences.
            </p>
            <Button size="lg" className="px-8">
              Get Started Today
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
