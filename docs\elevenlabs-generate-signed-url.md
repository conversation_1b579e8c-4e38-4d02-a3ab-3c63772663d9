# ElevenLabs Generate Signed URL API

This document describes how to use the ElevenLabs Generate Signed URL API endpoint.

## Overview

The Generate Signed URL API endpoint allows you to obtain a signed URL for an ElevenLabs agent, which can be used to establish a direct connection to the ElevenLabs service. Before providing the URL, the endpoint checks if the agent has sufficient budget (credits) available.

The endpoint retrieves the ElevenLabs agent ID from the `external_provider_id` field in the `agent_configs` table, looking for a configuration with `config_type` set to 'elevenlabs' or 'voice' with provider 'elevenlabs'.

## Endpoint

```
POST /api/elevenlabs/generate-signed-url
```

## Request Body

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| agentId | string | Yes | The ID of the agent in your database |
| estimatedCost | number | No | The estimated cost of the call in cents (default: 10) |

Example request body:

```json
{
  "agentId": "550e8400-e29b-41d4-a716-446655440000",
  "estimatedCost": 15
}
```

## Response

### Success Response

```json
{
  "url": "https://api.elevenlabs.io/v1/signed-url/...",
  "allow_call": true,
  "agent_id": "elevenlabs-agent-id",
  "success": true
}
```

### Error Responses

#### Missing Agent ID

```json
{
  "error": "Missing agentId parameter",
  "status": 400
}
```

#### Agent Not Found

```json
{
  "error": "Agent not found",
  "status": 404
}
```

#### ElevenLabs Agent ID Not Found

```json
{
  "error": "ElevenLabs agent ID not found for this agent",
  "status": 404
}
```

#### Insufficient Budget

```json
{
  "error": "Insufficient budget",
  "allow_call": false,
  "details": "There are not enough credits available to start this call.",
  "status": 403
}
```

## Usage Example

### JavaScript/TypeScript

```javascript
async function getSignedUrl(agentId) {
  try {
    const response = await fetch('/api/elevenlabs/generate-signed-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ agentId }),
    });
    
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to get signed URL');
    }

    return data.url;
  } catch (error) {
    console.error('Error getting signed URL:', error);
    throw error;
  }
}
```

## Differences from GET /api/elevenlabs/signed-url

This endpoint is similar to the existing `GET /api/elevenlabs/signed-url` endpoint but:

1. Uses a POST request instead of GET, which is more appropriate for operations that may have side effects
2. Takes parameters in the request body instead of query parameters
3. Looks for ElevenLabs agent ID in both 'elevenlabs' and 'voice' config types
4. Is designed to be used specifically for starting calls with ElevenLabs agents

## Budget Checking

The endpoint performs several budget checks before generating a signed URL:

1. Checks if the agent can start a call using the `can_start_call` RPC function
2. If that fails, it checks the organization's credit balance
3. It also checks the agent's daily budget limit if one is set
4. Finally, it checks if the team budget limit has been reached (if the agent belongs to a team)

Only if all budget checks pass will the endpoint generate and return a signed URL.
