'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { WidgetSettings } from '@/contexts/widget-context'
import Script from 'next/script'
import { useWidgetStore } from '@/stores/widget-store'

// Define the widget instance type
interface WidgetInstance {
  update: (config: any) => void;
  destroy: () => void;
}

// Add type declaration for window properties
declare global {
  interface Window {
    widgetInstance?: WidgetInstance;
    ConversationWidget: (config: any) => any;
  }
}

interface WidgetPreviewProps {
  agentId: string
  orgId?: string
}

// Component that uses the Zustand store
function WidgetPreviewWithStore({ agentId }: { agentId: string }) {
  // Import the store hook at the top level
  const { getSettingsForAgent } = useWidgetStore();

  // Get the settings for this agent
  const widgetSettings = getSettingsForAgent(agentId);

  // Listen for changes in widget settings
  useEffect(() => {
    const handleSettingsChanged = (event: Event) => {
      const detail = (event as CustomEvent).detail;

      // Only update if the event is for this agent
      if (!detail.agentId || detail.agentId === agentId) {
        console.log('Widget settings changed for this agent:', detail);
        // The WidgetPreviewCore will handle the update directly
      }
    };

    // Add event listener
    window.addEventListener('widget-settings-changed', handleSettingsChanged);

    // Cleanup
    return () => {
      window.removeEventListener('widget-settings-changed', handleSettingsChanged);
    };
  }, [agentId]);

  return (
    <WidgetPreviewCore
      agentId={agentId}
      externalSettings={widgetSettings}
    />
  );
}

// Main export component that uses the Zustand store
export function WidgetPreview({ agentId, orgId }: WidgetPreviewProps) {
  // Always use the Zustand store implementation
  return <WidgetPreviewWithStore agentId={agentId} />;
}

// Core implementation that uses the update function
function WidgetPreviewCore({ agentId, externalSettings }: { agentId: string, externalSettings: WidgetSettings }) {
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Store widget instance in a ref
  const widgetInstanceRef = useRef<WidgetInstance | null>(null);

  // Store settings in a ref to avoid re-renders
  const settingsRef = useRef(externalSettings);

  // Function to update the CSS
  const updateCSS = useCallback((settings: WidgetSettings) => {
    const styleElement = document.getElementById('widget-dynamic-css');
    if (styleElement) {
      // Base styles
      let cssContent = ""
      // `
      //   /* Widget Customization Styles */
        // ::part(widget-container) {
        //   /* Container customization */
        //   background: white;
        //   border-radius: 16px;
        //   box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        // }

      //   ::part(start-button) {
      //     /* Start button style */
      //     background-color: ${settings.primaryColor || '#2e62c9'};
      //     font-family: "Inter",Sans-serif;
      //     font-size: 16px;
      //     font-weight: 500;
      //     color: #fff;
      //     border-radius: 8px;
      //     box-shadow: inset 0 .5px .5px 0 rgba(255,255,255,.4);
      //   }

      //   ::part(end-button) {
      //     /* End button style */
      //     background-color: #e12525;
      //     font-family: "Inter",Sans-serif;
      //     color: #fff;
      //     border-radius: 8px;
      //   }

      //   ::part(avatar-container) {
      //     /* Avatar container style */
      //     color: white;
      //     background-color: ${settings.primaryColor || '#2e62c9'};
      //   }
      // `;

      // Add custom CSS if provided
      if (settings.customCSS && settings.customCSS.trim() !== '') {
        cssContent += `\n\n/* Custom CSS */\n${settings.customCSS}`;
      }

      styleElement.innerHTML = cssContent;
    }
  }, []);

  // Function to safely destroy the widget instance
  const destroyWidget = useCallback(() => {
    if (widgetInstanceRef.current) {
      try {
        // Check if destroy method exists
        if (typeof widgetInstanceRef.current.destroy === 'function') {
          widgetInstanceRef.current.destroy();
        } else {
          // If no destroy method, try to clean up manually
          if (containerRef.current) {
            containerRef.current.innerHTML = '';
          }
        }

        // Reset references regardless
        widgetInstanceRef.current = null;
        window.widgetInstance = undefined;
        console.log('Widget instance cleaned up');
      } catch (e) {
        console.error('Error cleaning up widget instance:', e);
        // Still try to reset references on error
        widgetInstanceRef.current = null;
        window.widgetInstance = undefined;
      }
    }
  }, [containerRef]);

  // Function to initialize the widget
  const initializeWidget = useCallback(() => {
    console.log("Initializing widget...");

    // Double-check that we have what we need
    if (!scriptLoaded) {
      console.log('Script not loaded yet, skipping initialization');
      return false;
    }

    if (typeof window === 'undefined' || typeof window.ConversationWidget !== 'function') {
      console.error('ConversationWidget is not available');
      return false;
    }

    // Check if containerRef is available
    if (!containerRef.current) {
      console.error('Container ref not available');
      return false;
    }

    // Get the current settings from the ref
    const settings = settingsRef.current;

    try {
      // Update the dynamic CSS
      updateCSS(settings);

      // First destroy any existing widget
      destroyWidget();

      console.log('Creating new widget instance with settings:', {
        agentId,
        position: settings.position,
        containerId: 'widget-container-preview'
      });

      // Create a new widget instance
      const instance = window.ConversationWidget({
        agentId: agentId,
        position: settings.position || 'bottom-right',
        containerId: 'widget-container-preview',
        type: settings.widgetType || 'standalone',
        customParts: {
          container: 'widget-container',
          startButton: 'start-button',
          endButton: 'end-button',
          avatarContainer: 'avatar-container',
          statusText: 'status-text',
          ...(settings.hideIcon ? { hideIcon: true } : {}),
          ...(settings.customIcon ? { customIcon: settings.customIcon } : {}),
          customText: settings.customText
        }
      });

      // Store the instance in our refs
      widgetInstanceRef.current = instance;
      window.widgetInstance = instance;

      console.log('Widget initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing widget:', error);
      return false;
    }
  }, [agentId, scriptLoaded, containerRef, destroyWidget, updateCSS]);

  // Function to update the widget with current settings
  const updateWidget = useCallback(() => {
    if (!widgetInstanceRef.current) {
      console.log('No widget instance to update, initializing instead');
      initializeWidget();
      return;
    }

    console.log('Updating widget with new settings');
    const settings = settingsRef.current;

    try {
      // Update the dynamic CSS
      updateCSS(settings);

      // Update the widget instance with new settings
      widgetInstanceRef.current.update({
        position: settings.position || 'bottom-right',
        type: settings.widgetType || 'standalone',
        customParts: {
          hideIcon: settings.hideIcon,
          customIcon: settings.customIcon,
          customText: settings.customText
        }
      });

      console.log('Widget updated successfully');
    } catch (error) {
      console.error('Error updating widget:', error);
    }
  }, [initializeWidget, updateCSS]);

  // Update the ref when props change
  useEffect(() => {
    settingsRef.current = externalSettings;

    // If widget instance exists, update it with new settings
    if (widgetInstanceRef.current && scriptLoaded) {
      updateWidget();
    }
  }, [externalSettings, scriptLoaded, updateWidget]);

  // Track component lifecycle
  useEffect(() => {
    console.log('WidgetPreviewCore mounted');

    return () => {
      console.log('WidgetPreviewCore unmounting');
      destroyWidget();
    };
  }, [destroyWidget]);

  // Handle script load with verification
  const handleScriptLoad = useCallback(() => {
    console.log('Script load event triggered');

    // Verify that the ConversationWidget is actually available
    if (typeof window !== 'undefined' && typeof window.ConversationWidget === 'function') {
      console.log('ConversationWidget is available, setting scriptLoaded to true');
      setScriptLoaded(true);
    } else {
      console.warn('Script loaded but ConversationWidget is not available');
      // Wait a bit and check again
      setTimeout(() => {
        if (typeof window !== 'undefined' && typeof window.ConversationWidget === 'function') {
          console.log('ConversationWidget is now available after delay');
          setScriptLoaded(true);
        } else {
          console.error('ConversationWidget still not available after delay');
          // Try to reload the script
          const scriptElement = document.createElement('script');
          scriptElement.src = `${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js`;
          scriptElement.onload = () => {
            console.log('Script reloaded manually');
            if (typeof window.ConversationWidget === 'function') {
              setScriptLoaded(true);
            }
          };
          document.head.appendChild(scriptElement);
        }
      }, 500);
    }
  }, []);

  // Initialize widget when script is loaded
  useEffect(() => {
    if (scriptLoaded && !widgetInstanceRef.current) {
      console.log('Script loaded, initializing widget');
      setTimeout(() => {
        initializeWidget();
      }, 300);
    }
  }, [scriptLoaded, initializeWidget]);

  // Listen for widget settings changes via events
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Event handler for widget settings changes
    const handleWidgetSettingsChanged = (event: Event) => {
      const detail = (event as CustomEvent).detail;

      // Only proceed if this is for our agent or no agent is specified
      if (!detail.agentId || detail.agentId === agentId) {
        console.log('Widget settings changed event received, updating widget', detail);

        if (scriptLoaded && widgetInstanceRef.current) {
          // Use the update function instead of reinitializing
          updateWidget();
        } else if (scriptLoaded) {
          // If no widget instance exists yet, initialize it
          initializeWidget();
        }
      }
    };

    // Add event listener
    window.addEventListener('widget-settings-changed', handleWidgetSettingsChanged);

    // Cleanup
    return () => {
      window.removeEventListener('widget-settings-changed', handleWidgetSettingsChanged);
    };
  }, [agentId, scriptLoaded, updateWidget, initializeWidget]);

  return (
    <div className="relative h-full w-full flex flex-col">
      {/* Load the widget script with error handling */}
      
      <Script
        src={`${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js`}
        onLoad={handleScriptLoad}
        onError={(e) => {
          console.error('Error loading widget script:', e);
          // Try to reload the script after a delay
          setTimeout(() => {
            const scriptElement = document.createElement('script');
            scriptElement.src = `${process.env.NEXT_PUBLIC_WIDGET_URL}/widget.iife.js`;
            scriptElement.onload = handleScriptLoad;
            document.head.appendChild(scriptElement);
          }, 1000);
        }}
        strategy="afterInteractive"
      />

      {/* Add the CSS from the documentation directly */}
      <style id="widget-dynamic-css"></style>

      <Card className="flex-1 flex flex-col w-full h-full">
        <CardContent className="flex-1 relative bg-zinc-100 dark:bg-zinc-900 rounded-md overflow-hidden p-2 h-full">

          {/* Widget container - this is where the widget will be rendered */}
          <div className="absolute inset-0 flex items-center justify-center">
            {!scriptLoaded ? (
              <div className="text-center">
                <p className="text-muted-foreground text-sm">Loading widget...</p>
              </div>
            ) : (
              <div
                ref={containerRef}
                className="w-full h-full relative"
                id="widget-container-preview"
                data-agent-id={agentId}
              >
                {/* The widget will be loaded here directly */}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
