'use client'

import { cn } from "@/utils/cn"
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search } from 'lucide-react'
import { useState, useTransition, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useDebounce } from '@/hooks/useDebounce'

interface AgentSidebarProps {
  orgId: string
  currentAgentId?: string
  initialAgents: any[]
  initialCount: number
  initialHasMore: boolean
  initialSearch: string
  initialPage: number
}

// Add this type for the API response
interface AgentsResponse {
  data: any[]
  count: number
  hasMore: boolean
  error: any
}

export function AgentSidebar({
  orgId,
  currentAgentId,
  initialAgents,
  initialCount,
  initialHasMore,
  initialSearch,
  initialPage
}: AgentSidebarProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isPending, startTransition] = useTransition()
  const [agents, setAgents] = useState(initialAgents)
  const [count, setCount] = useState(initialCount)
  const [hasMore, setHasMore] = useState(initialHasMore)
  const [search, setSearch] = useState(initialSearch)
  const [page, setPage] = useState(initialPage)

  const debouncedSearch = useDebounce(search, 300)

  // Add fetchAgents function
  const fetchAgents = async (searchQuery: string, pageNum: number) => {
    try {
      const params = new URLSearchParams({
        search: searchQuery,
        page: String(pageNum)
      })
      
      const response = await fetch(`/api/organizations/${orgId}/agents?${params}`)
      const data: AgentsResponse = await response.json()
      
      if (pageNum === 1) {
        // Reset list for new searches
        setAgents(data.data)
      } else {
        // Append for "load more"
        setAgents(prev => [...prev, ...data.data])
      }
      
      setCount(data.count)
      setHasMore(data.hasMore)
    } catch (error) {
      console.error('Error fetching agents:', error)
    }
  }

  // Add effect for debounced search
  useEffect(() => {
    if (debouncedSearch !== initialSearch) {
      setPage(1) // Reset page when search changes
      fetchAgents(debouncedSearch, 1)
      
      // Update URL
      startTransition(() => {
        const params = new URLSearchParams(searchParams)
        if (debouncedSearch) {
          params.set('search', debouncedSearch)
        } else {
          params.delete('search')
        }
        params.set('page', '1')
        router.replace(`/dashboard/${orgId}/agents/${currentAgentId}?${params.toString()}`)
      })
    }
  }, [debouncedSearch])

  // Modify handleLoadMore to use fetchAgents
  const handleLoadMore = () => {
    const nextPage = page + 1
    setPage(nextPage)
    fetchAgents(search, nextPage)
  }

  // Modify handleSearchChange to only update local state
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value)
  }

  return (
    <nav className="p-4 space-y-4">
      <p className="text-muted-foreground">Agent List</p>
      
      {/* Search form */}
      <div className="relative">
        <Input
          type="search"
          value={search}
          onChange={handleSearchChange}
          placeholder="Search agents..."
          className="pr-8"
        />
        <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
      </div>

      {/* Agents list */}
      <div className="space-y-2">
        {agents?.map(agent => (
          <Link 
            className={cn(
              "block p-2 rounded text-sm",
              agent.id === currentAgentId 
                ? "bg-slate-100 font-medium" 
                : "hover:bg-slate-50"
            )}
            key={agent.id} 
            href={`/dashboard/${orgId}/agents/${agent.id}`}
          >
            {agent.name}
          </Link>
        ))}
      </div>

      {/* Load more button */}
      {hasMore && (
        <Button 
          onClick={handleLoadMore}
          variant="ghost" 
          className="w-full"
          disabled={isPending}
        >
          {isPending ? 'Loading...' : 'Load More'}
        </Button>
      )}

      {/* Show count */}
      <p className="text-sm text-muted-foreground">
        Showing {agents?.length} of {count} agents
      </p>
    </nav>
  )
}