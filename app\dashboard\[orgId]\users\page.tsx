import { getOrganizationUsers } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'
import { UsersTable } from '@/components/users/users-table'


interface PageProps  {
  params: Promise<{
    orgId: string,
  }>,
  searchParams?: Promise<{
    search?: string,
    page?: string
  }>
}


export default async function Page({ params, searchParams }: PageProps) {
  const { orgId } = await params;
  const awaitedSearchParams = await searchParams;
  const search = awaitedSearchParams?.search || '';
  const page = Number(awaitedSearchParams?.page) || 1;
  const pageSize = 10;

  const supabase = await createClient();
  const {data:users, error} = await getOrganizationUsers(
    supabase,
    orgId,
    search,
    page,
    pageSize
  );
    if (error) {
        console.error(error)
        return <div className="p-4">Error fetching users</div>
    }

    return (
      <div className="p-6">
        <UsersTable users={users || []} orgId={orgId} />
      </div>
    )
}