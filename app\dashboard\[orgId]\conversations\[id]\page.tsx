import { notFound } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate, formatTime, formatDuration, formatCurrency } from '@/utils/format'
import {
  ArrowLeft,
  Clock,
  MessageSquare,
  CheckCircle2,
  XCircle,
  DollarSign,
  User,
  Bot
} from 'lucide-react'
import Link from 'next/link'
import { createClient } from '@/utils/supabase/server'
import { getConversationDetails } from '@/utils/supabase/queries'

interface PageProps {
  params: Promise<{ orgId: string, id: string }>
}

export default async function Page({ params }: PageProps) {
  const { orgId, id } = await params;

  // Fetch the conversation from the database
  const supabase = await createClient();
  const conversation = await getConversationDetails(supabase, id);

  if (!conversation) {
    return notFound();
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link href={`/dashboard/${orgId}/conversations`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Conversations
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Agent</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold">{conversation.agent_name}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Date & Time</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold">{formatDate(conversation.timestamp)}</p>
            <p className="text-sm text-muted-foreground">{formatTime(conversation.timestamp)}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Status</CardTitle>
          </CardHeader>
          <CardContent>
            {conversation.status === 'successful' ? (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1 w-fit">
                <CheckCircle2 className="h-3 w-3" />
                Successful
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1 w-fit">
                <XCircle className="h-3 w-3" />
                Failed
              </Badge>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Duration</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center">
            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
            <p className="text-lg font-semibold">{formatDuration(conversation.duration_seconds)}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Messages</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-2 text-muted-foreground" />
            <p className="text-lg font-semibold">{conversation.message_count}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Cost</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center">
            <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
            <p className="text-lg font-semibold">${formatCurrency(conversation.cost_cents || 0)}</p>
          </CardContent>
        </Card>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Transcript</h2>
        <div className="space-y-4">
          {conversation.transcript?.map((message, index) => (
            <div
              key={index}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : ''}`}
            >
              {message.role === 'agent' && (
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Bot className="h-4 w-4 text-primary" />
                </div>
              )}

              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <div className="mb-1 text-xs opacity-70">
                  {formatTime(message.timestamp)}
                </div>
                <p>{message.content}</p>
              </div>

              {message.role === 'user' && (
                <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  <User className="h-4 w-4 text-primary-foreground" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}