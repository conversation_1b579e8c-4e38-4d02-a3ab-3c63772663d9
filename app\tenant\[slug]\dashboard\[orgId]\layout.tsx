import { Suspense } from 'react';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/app-sidebar';
import { SiteHeader } from '@/components/site-header';
import { Toaster } from '@/components/ui/Toasts/toaster';
import { createClient } from '@/utils/supabase/server';
import { getUserOrganizations } from '@/utils/supabase/queries';
import { getTenantContext } from '@/utils/tenant/context';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';

interface TenantDashboardLayoutProps {
  params: Promise<{ slug: string; orgId: string }>;
  children: React.ReactNode;
}

export default async function TenantDashboardLayout({ params, children }: TenantDashboardLayoutProps) {
  const { slug, orgId } = await params;
  const { tenant } = await getTenantContext(slug);

  if (!tenant) {
    return redirect('/');
  }

  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return redirect(`/tenant/${slug}/signin`);
  }

  // Get all organizations for this user
  const organizations = await getUserOrganizations(supabase);

  // Check if the user has access to the requested organization
  const { data: membership } = await supabase
    .from('organization_memberships')
    .select('organization_id')
    .eq('user_id', user.id)
    .eq('organization_id', orgId)
    .single();

  if (!membership) {
    // User doesn't have access to this organization
    return redirect(`/tenant/${slug}`);
  }

  // Check if the organization belongs to this tenant
  const { data: organization } = await supabase
    .from('organizations')
    .select('tenant_id')
    .eq('id', orgId)
    .single();

  if (!organization || organization.tenant_id !== tenant.id) {
    // Organization doesn't belong to this tenant
    return redirect(`/tenant/${slug}`);
  }

  // Set the last organization ID cookie
  const cookieStore = await cookies();
  cookieStore.set('last_org_id', orgId, { path: '/' });

  return (
    <>
      <main
        id="skip"
        className="text-zinc-900 dark:text-zinc-50"
      >
        <SidebarProvider>
          <AppSidebar
            variant="inset"
            user={user}
            organizations={organizations}
            currentOrganization={orgId}
            tenantSlug={slug}
          />
          <SidebarInset>
            <SiteHeader tenantSlug={slug} />
            {children}
          </SidebarInset>
        </SidebarProvider>
      </main>
      <Suspense>
        <Toaster />
      </Suspense>
    </>
  );
}
