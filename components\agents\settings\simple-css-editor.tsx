'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'

// Define a simple interface for CSS properties
interface CssProperty {
  property: string;
  value: string;
  displayName: string;
  type: 'color' | 'text' | 'number' | 'slider';
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
}

// Define a part with its properties
interface CssPart {
  selector: string;
  displayName: string;
  properties: CssProperty[];
}

// Define the props for the component
interface SimpleCssEditorProps {
  value: string;
  onChange: (css: string) => void;
}

// Define the parts and their properties
const widgetParts: CssPart[] = [
  {
    selector: 'widget-container',
    displayName: 'Widget Container',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  },
  {
    selector: 'widget',
    displayName: 'Widget Body',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  },
  {
    selector: 'start-button',
    displayName: 'Start Button',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'color', value: '', displayName: 'Text Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  },
  {
    selector: 'end-button',
    displayName: 'End Button',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'color', value: '', displayName: 'Text Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  },
  {
    selector: 'user-message',
    displayName: 'User Message Bubbles',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'color', value: '', displayName: 'Text Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  },
  {
    selector: 'ai-message',
    displayName: 'AI Message Bubbles',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'color', value: '', displayName: 'Text Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  },
  {
    selector: 'send-button',
    displayName: 'Send Button',
    properties: [
      { property: 'background-color', value: '', displayName: 'Background Color', type: 'color' },
      { property: 'color', value: '', displayName: 'Text Color', type: 'color' },
      { property: 'border-radius', value: '', displayName: 'Border Radius', type: 'slider', min: 0, max: 20, step: 1, unit: 'px' }
    ]
  }
];

export function SimpleCssEditor({ value, onChange }: SimpleCssEditorProps) {
  // State to store the current CSS values
  const [parts, setParts] = useState<CssPart[]>(widgetParts);

  // Parse the CSS when the component mounts or when the value changes
  useEffect(() => {
    if (!value) return;

    try {
      // Create a deep copy of the parts to avoid state mutation issues
      const newParts = JSON.parse(JSON.stringify(parts));

      // For each part, try to find its properties in the CSS
      newParts.forEach((part: CssPart) => {
        const regex = new RegExp(`::part\\(${part.selector}\\)\\s*{([^}]*)}`, 'g');
        const match = regex.exec(value);

        if (match && match[1]) {
          const cssBlock = match[1];

          // For each property, try to find its value in the CSS block
          part.properties.forEach(prop => {
            // Use a more precise regex that matches the exact property
            const propRegex = new RegExp(`(^|\\s+)${prop.property}:\\s*([^;]+);`, 'm');
            const propMatch = propRegex.exec(cssBlock);

            if (propMatch && propMatch[2]) {
              prop.value = propMatch[2].trim();
              console.log(`Parsed ${part.selector} - ${prop.property}: ${prop.value}`);
            } else {
              // Reset the property if it's not found in the CSS
              prop.value = '';
            }
          });
        } else {
          // Reset all properties if the part is not found in the CSS
          part.properties.forEach(prop => {
            prop.value = '';
          });
        }
      });

      // Update the state
      setParts(newParts);
    } catch (error) {
      console.error('Error parsing CSS:', error);
    }
  }, [value]);

  // Generate CSS from the current state
  const generateCss = () => {
    let css = '';

    // For each part, generate the CSS
    parts.forEach(part => {
      // Only include parts with at least one property value
      const hasValues = part.properties.some(prop => prop.value);
      if (!hasValues) return;

      css += `::part(${part.selector}) {\n`;

      // For each property, add it to the CSS if it has a value
      part.properties.forEach(prop => {
        if (prop.value) {
          // Add the unit if specified and not already included in the value
          const valueWithUnit = prop.unit && !prop.value.endsWith(prop.unit)
            ? `${prop.value}${prop.unit}`
            : prop.value;

          css += `  ${prop.property}: ${valueWithUnit};\n`;
        }
      });

      css += '}\n\n';
    });

    return css;
  };

  // Update a property value
  const updateProperty = (partIndex: number, propIndex: number, value: string) => {
    // Create a deep copy of the parts array to avoid state mutation issues
    const newParts = JSON.parse(JSON.stringify(parts));

    // Update the specific property
    newParts[partIndex].properties[propIndex].value = value;

    // Update the state
    setParts(newParts);

    // Generate the CSS and call the onChange callback
    const css = generateCss();
    onChange(css);

    // Log for debugging
    console.log(`Updated ${newParts[partIndex].selector} - ${newParts[partIndex].properties[propIndex].property} to ${value}`);
    console.log('Generated CSS:', css);
  };

  // Render the editor
  return (
    <div className="space-y-4">
      <Accordion type="single" collapsible className="w-full">
        {parts.map((part, partIndex) => (
          <AccordionItem key={part.selector} value={part.selector}>
            <AccordionTrigger>{part.displayName}</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4 p-2">
                {part.properties.map((prop, propIndex) => (
                  <div key={prop.property} className="grid grid-cols-2 items-center gap-2">
                    <Label>{prop.displayName}</Label>
                    {prop.type === 'color' ? (
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={prop.value && prop.value.startsWith('#') ? prop.value : '#ffffff'}
                          onChange={e => {
                            // Only update with the color picker value
                            updateProperty(partIndex, propIndex, e.target.value);
                          }}
                          className="w-12 p-1 h-10"
                        />
                        <Input
                          value={prop.value || ''}
                          onChange={e => {
                            // Update with the text input value
                            updateProperty(partIndex, propIndex, e.target.value);
                          }}
                          placeholder="transparent"
                          id={`${part.selector}-${prop.property}-text`}
                        />
                      </div>
                    ) : prop.type === 'slider' ? (
                      <div className="flex gap-2 items-center">
                        <Slider
                          min={prop.min || 0}
                          max={prop.max || 100}
                          step={prop.step || 1}
                          value={[prop.value ? parseInt(prop.value) : 0]}
                          onValueChange={values => updateProperty(partIndex, propIndex, values[0].toString())}
                          className="flex-1"
                        />
                        <span className="w-12 text-center">{prop.value || 0}{prop.unit}</span>
                      </div>
                    ) : (
                      <Input
                        value={prop.value || ''}
                        onChange={e => updateProperty(partIndex, propIndex, e.target.value)}
                      />
                    )}
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}

