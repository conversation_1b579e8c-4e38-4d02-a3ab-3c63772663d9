import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/utils/cn"
import { useState } from "react"

const dummyData = [
  { id: 1, name: "<PERSON>" },
  { id: 2, name: "<PERSON>" },
  { id: 3, name: "<PERSON>" },
  { id: 4, name: "<PERSON>" },
  { id: 5, name: "<PERSON>" },
  { id: 6, name: "<PERSON>" },
  { id: 7, name: "<PERSON>" },
  { id: 8, name: "<PERSON>" }
]

export default function SidebarList({ className, onSelect }: { className?: string; onSelect?: (item: any) => void }) {
  const [search, setSearch] = useState("")

  const filtered = dummyData.filter((item) =>
    item.name.toLowerCase().includes(search.toLowerCase())
  )

  return (
    <div className={cn("p-4 border-r w-full h-full flex flex-col", className)}>
      <Input
        placeholder="Search..."
        className="mb-4"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />
      <ScrollArea className="flex-1 overflow-auto">
        <ul className="space-y-1">
          {filtered.map((item) => (
            <li
              key={item.id}
              className="cursor-pointer px-3 py-2 rounded hover:bg-muted transition"
              onClick={() => onSelect?.(item)}
            >
              {item.name}
            </li>
          ))}
          {filtered.length === 0 && (
            <li className="text-muted-foreground text-sm">No results found.</li>
          )}
        </ul>
      </ScrollArea>
    </div>
  )
}
