// This is a simple test script to verify the implementation of the signed URL endpoint
// It mocks the necessary dependencies and tests the logic

// Mock the NextResponse
const NextResponse = {
  json: (data, options) => ({ data, options })
};

// Mock the createClient function
const createClient = async () => ({
  from: (table) => {
    return {
      select: (columns) => {
        return {
          eq: (column, value) => {
            if (column === 'id' && value === 'test-agent-id' && table === 'agents') {
              return {
                single: () => ({
                  data: { id: 'test-agent-id', organization_id: 'test-org-id', team_id: 'test-team-id' },
                  error: null
                })
              };
            } else if (column === 'agent_id' && value === 'test-agent-id' && table === 'agent_configs') {
              return {
                eq: (column2, value2) => {
                  return {
                    eq: (column3, value3) => {
                      return {
                        maybeSingle: () => ({
                          data: {
                            external_provider_id: 'test-elevenlabs-agent-id'
                          },
                          error: null
                        })
                      };
                    }
                  };
                }
              };
            } else if (column === 'agent_id' && value === 'test-agent-id' && table === 'agent_budget_limits') {
              return {
                maybeSingle: () => ({
                  data: { daily_limit_cents: 1000 },
                  error: null
                })
              };
            } else if (column === 'organization_id' && value === 'test-org-id' && table === 'credit_wallets') {
              return {
                single: () => ({
                  data: { balance_cents: 5000 },
                  error: null
                })
              };
            } else {
              return {
                single: () => ({ data: null, error: null }),
                maybeSingle: () => ({ data: null, error: null })
              };
            }
          },
          gte: (column, value) => ({
            data: [{ amount_cents: -5 }, { amount_cents: -10 }],
            error: null
          })
        };
      }
    };
  },
  rpc: (funcName, params) => ({
    data: true, // Simulate successful budget check
    error: null
  })
});

// Mock the elevenlabsQueries
const elevenlabsQueries = {
  getSignedUrl: async (agentId) => 'https://api.elevenlabs.io/v1/signed-url/test-url'
};

// Mock the Request object
const request = {
  url: 'http://localhost:3000/api/elevenlabs/signed-url?agentId=test-agent-id'
};

// Define the handler function (simplified version of the actual implementation)
async function handler(request) {
  try {
    // Get the agent ID from the query parameters
    const url = new URL(request.url);
    const agentId = url.searchParams.get('agentId');

    if (!agentId) {
      return NextResponse.json(
        { error: 'Missing agentId parameter' },
        { status: 400 }
      );
    }

    // Default estimated cost (in cents)
    const estimatedCost = parseInt(url.searchParams.get('estimatedCost') || '10');

    // Create Supabase client
    const supabase = await createClient();

    // Get the agent to verify it exists
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      console.error('Error fetching agent:', agentError);
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    // Get the agent config to find the ElevenLabs agent ID
    const { data: agentConfig, error: configError } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', agentId)
      .eq('config_type', 'internal')
      .eq('provider', 'elevenlabs')
      .maybeSingle();

    if (configError) {
      console.error('Error fetching agent config:', configError);
      return NextResponse.json(
        { error: 'Error fetching agent configuration' },
        { status: 500 }
      );
    }

    // Extract the ElevenLabs agent ID from the external_provider_id field
    let elevenLabsAgentId = agentConfig?.external_provider_id;

    if (!elevenLabsAgentId) {
      return NextResponse.json(
        { error: 'ElevenLabs agent ID not found for this agent' },
        { status: 404 }
      );
    }

    // Check if the agent has enough budget
    const { data: canStartCall, error: budgetError } = await supabase.rpc('can_start_call', {
      agent: agentId,
      estimated_cost: estimatedCost
    });

    if (budgetError) {
      console.error('Error checking budget:', budgetError);
      return NextResponse.json(
        {
          error: 'Error checking budget',
          details: budgetError.message
        },
        { status: 500 }
      );
    }

    if (!canStartCall) {
      return NextResponse.json(
        {
          error: 'Insufficient budget',
          allow_call: false,
          details: 'There are not enough credits available to start this call.'
        },
        { status: 403 }
      );
    }

    // Get the signed URL from ElevenLabs
    try {
      const signedUrl = await elevenlabsQueries.getSignedUrl(elevenLabsAgentId);

      return NextResponse.json({
        url: signedUrl,
        allow_call: true,
        agent_id: elevenLabsAgentId,
        success: true
      });
    } catch (error) {
      console.error('Error getting signed URL from ElevenLabs:', error);

      return NextResponse.json(
        {
          error: 'Failed to get signed URL from ElevenLabs',
          details: error instanceof Error ? error.message : String(error),
          success: false
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in signed URL endpoint:', error);

    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error),
        success: false
      },
      { status: 500 }
    );
  }
}

// Run the test
async function runTest() {
  console.log('Testing signed URL endpoint...');
  const response = await handler(request);
  console.log('Response:', JSON.stringify(response, null, 2));

  // Test with missing agent ID
  const requestWithoutAgentId = {
    url: 'http://localhost:3000/api/elevenlabs/signed-url'
  };
  const errorResponse = await handler(requestWithoutAgentId);
  console.log('Error response:', JSON.stringify(errorResponse, null, 2));
}

runTest().catch(console.error);
