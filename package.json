{"private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prettier-fix": "prettier --write .", "stripe:login": "stripe login", "stripe:listen": "stripe listen --forward-to=localhost:3000/api/webhooks", "stripe:fixtures": "stripe fixtures fixtures/stripe-fixtures.json", "stripe:sync-products": "node scripts/syncProducts.js", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:status": "npx supabase status", "supabase:restart": "npm run supabase:stop && npm run supabase:start", "supabase:reset": "npx supabase db reset", "supabase:link": "npx supabase link", "supabase:generate-types": "npx supabase gen types typescript --local --schema public > types_db.ts", "supabase:generate-migration": "npx supabase db diff | npx supabase migration new", "supabase:generate-seed": "npx supabase db dump --data-only -f supabase/seed.sql", "supabase:push": "npx supabase db push", "supabase:pull": "npx supabase db pull"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/stripe-js": "2.4.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.43.4", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "elevenlabs": "^1.54.0", "lucide-react": "0.330.0", "next": "15.2.3", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-merge-refs": "^2.1.1", "recharts": "^2.15.1", "sonner": "^2.0.1", "stripe": "^14.25.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.4"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-tailwindcss": "^3.17.3", "postcss": "^8.4.38", "prettier": "^3.3.1", "prettier-plugin-tailwindcss": "^0.5.14", "supabase": "^1.172.2", "typescript": "^5.4.5"}, "pnpm": {"onlyBuiltDependencies": ["supabase"]}}