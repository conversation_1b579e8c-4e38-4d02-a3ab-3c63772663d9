/* CSS for flag fallbacks */
[data-content]::before {
  content: attr(data-content);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-weight: bold;
  color: #333;
  background-color: #f0f0f0;
  font-size: 0.8em;
}

.dark [data-content]::before {
  color: #e0e0e0;
  background-color: #333;
}

/* Ensure the command items are clickable */
[cmdk-item] {
  cursor: pointer !important;
  user-select: none;
  position: relative;
}

[cmdk-item]:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark [cmdk-item]:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[cmdk-item][aria-selected="true"] {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark [cmdk-item][aria-selected="true"] {
  background-color: rgba(255, 255, 255, 0.1);
}

[cmdk-item][data-disabled="true"] {
  opacity: 0.5;
  cursor: not-allowed !important;
}

/* Add a clickable overlay to ensure clicks work */
[cmdk-item]::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Make sure the content is above the overlay */
[cmdk-item] > * {
  position: relative;
  z-index: 2;
}
