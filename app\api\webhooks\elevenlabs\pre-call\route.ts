import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    // Get the agent ID from the query parameters
    const url = new URL(request.url);
    const agentId = url.searchParams.get('agentId');
    
    if (!agentId) {
      return NextResponse.json(
        { error: 'Missing agentId parameter' },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await request.json();
    const { conversation_id, estimated_cost_cents } = body;
    
    // Default to a reasonable cost if not provided
    const cost = estimated_cost_cents || 10; // Default to 10 cents if not provided
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Call the can_start_call function to check if the agent has enough balance
    const { data, error } = await supabase.rpc('can_start_call', {
      agent: agentId,
      estimated_cost: cost
    });
    
    if (error) {
      console.error('Error checking call eligibility:', error);
      return NextResponse.json(
        { 
          allow_call: false,
          error: 'Error checking call eligibility',
          details: error.message
        },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json({
      allow_call: data === true,
      conversation_id,
      message: data === true ? 'Call authorized' : 'Insufficient credits'
    });
  } catch (error) {
    console.error('Error in pre-call webhook:', error);
    
    return NextResponse.json(
      { 
        allow_call: false,
        error: 'Failed to process pre-call webhook',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
