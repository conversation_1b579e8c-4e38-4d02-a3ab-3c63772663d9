const { createClient } = require('@supabase/supabase-js');
const { config } = require('dotenv');
const path = require('path');
const Stripe = require('stripe');

// Load environment variables from project root
config({ path: path.resolve(__dirname, '../.env.local') });

if (!process.env.STRIPE_SECRET_KEY) {
    console.error('❌ Missing STRIPE_SECRET_KEY in .env.local');
    process.exit(1);
}

if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Missing Supabase credentials in .env.local');
    process.exit(1);
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function syncStripeToSupabase() {
    try {
        console.log('🔄 Syncing Stripe products and prices...');
        
        const { data: products } = await stripe.products.list();
        console.log(`📦 Found ${products.length} products`);

        for (const product of products) {
            await supabase.from('products').upsert({
                id: product.id,
                active: product.active,
                name: product.name,
                description: product.description,
                image: product.images?.[0] || null,
                metadata: product.metadata
            });
        }

        const { data: prices } = await stripe.prices.list();
        console.log(`💰 Found ${prices.length} prices`);

        for (const price of prices) {
            await supabase.from('prices').upsert({
                id: price.id,
                product_id: price.product,
                active: price.active,
                description: price.nickname,
                unit_amount: price.unit_amount,
                currency: price.currency,
                type: price.type === 'recurring' ? 'recurring' : 'one_time',
                interval: price.recurring?.interval || null,
                interval_count: price.recurring?.interval_count || null,
                trial_period_days: price.recurring?.trial_period_days || null,
                metadata: price.metadata
            });
        }

        console.log('✅ Sync completed successfully!');
    } catch (error) {
        console.error('❌ Sync failed:', error);
        process.exit(1);
    }
}

syncStripeToSupabase();