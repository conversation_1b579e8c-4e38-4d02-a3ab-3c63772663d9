"use client"

import { Mail<PERSON>con, PlusCircleIcon, type LucideIcon } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
  }[]
}) {
  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        {/* <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <SidebarMenuButton
              tooltip="Quick Create"
              className="min-w-8 bg-zinc-900 text-zinc-50 duration-200 ease-linear hover:bg-zinc-900/90 hover:text-zinc-50 active:bg-zinc-900/90 active:text-zinc-50 dark:bg-zinc-50 dark:text-zinc-900 dark:hover:bg-zinc-50/90 dark:hover:text-zinc-900 dark:active:bg-zinc-50/90 dark:active:text-zinc-900"
            >
              <PlusCircleIcon />
              <span>Quick Create</span>
            </SidebarMenuButton>
            <Button
              size="icon"
              className="h-9 w-9 shrink-0 group-data-[collapsible=icon]:opacity-0"
              variant="outline"
            >
              <MailIcon />
              <span className="sr-only">Inbox</span>
            </Button>
          </SidebarMenuItem>
        </SidebarMenu> */}
        <SidebarMenu>
          {items.map((item) => (
             <Link href={item.url} key={item.title}>
            <SidebarMenuItem>
             
              <SidebarMenuButton tooltip={item.title}>
                {item.icon && <item.icon />}
                {item.title}
              </SidebarMenuButton>
             
            </SidebarMenuItem>
            </Link>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
