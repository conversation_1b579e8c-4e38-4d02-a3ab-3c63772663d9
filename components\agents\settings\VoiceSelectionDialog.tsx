'use client'

import { useState, useEffect, useRef } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  Di<PERSON>Trigger,
  DialogFooter
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Spinner } from '@/components/ui/spinner'
import { Badge } from '@/components/ui/badge'
import { Check, Play, Pause } from 'lucide-react'

// Types for ElevenLabs voices
interface ElevenLabsVoice {
  voice_id: string
  name: string
  category?: string
  description?: string
  preview_url?: string
  labels?: Record<string, string>
}

interface VoiceSelectionDialogProps {
  selectedVoiceId: string
  onVoiceSelect: (voiceId: string) => void
  triggerComponent?: React.ReactNode
  filteredVoices?: ElevenLabsVoice[]
}

// Function to fetch voices from ElevenLabs API
async function fetchElevenLabsVoices(): Promise<ElevenLabsVoice[]> {
  try {
    const response = await fetch('/api/elevenlabs/voices')
    if (!response.ok) {
      throw new Error('Failed to fetch voices')
    }
    const data = await response.json()
    return data.voices || []
  } catch (error) {
    console.error('Error fetching ElevenLabs voices:', error)
    throw error
  }
}

export function VoiceSelectionDialog({
  selectedVoiceId,
  onVoiceSelect,
  triggerComponent,
  filteredVoices
}: VoiceSelectionDialogProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [open, setOpen] = useState(false)
  const [tempSelectedVoice, setTempSelectedVoice] = useState(selectedVoiceId)
  const [playingVoiceId, setPlayingVoiceId] = useState<string | null>(null)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)

  // Cleanup effect for component unmounting
  useEffect(() => {
    return () => {
      if (audioElement) {
        audioElement.pause()
        audioElement.currentTime = 0
      }
    }
  }, [audioElement])

  // Use React Query to fetch and cache ElevenLabs voices
  const { data: voices, isLoading, error } = useQuery({
    queryKey: ['elevenlabs-voices'],
    queryFn: fetchElevenLabsVoices,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2
  })

  // Filter voices based on search query and provided filtered voices
  const displayVoices = filteredVoices || voices
  const searchFilteredVoices = displayVoices?.filter((voice: ElevenLabsVoice) =>
    voice.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (voice.labels?.accent && voice.labels.accent.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // Group voices by category
  const groupedVoices: Record<string, ElevenLabsVoice[]> = {}

  // First, add the "Recent" category if we have a selected voice
  groupedVoices["Recent"] = []

  // Then add "Perfect for Conversational AI" category
  groupedVoices["Perfect for Conversational AI"] = []

  // Process all voices
  searchFilteredVoices?.forEach((voice: ElevenLabsVoice) => {
    // Add to Recent if it's the selected voice
    if (voice.voice_id === selectedVoiceId) {
      groupedVoices["Recent"].push(voice)
    }

    // Add to appropriate category
    const category = voice.category || "Other"
    if (!groupedVoices[category]) {
      groupedVoices[category] = []
    }
    groupedVoices[category].push(voice)
  })

  // Remove empty categories
  Object.keys(groupedVoices).forEach(category => {
    if (groupedVoices[category].length === 0) {
      delete groupedVoices[category]
    }
  })

  const handleConfirm = () => {
    onVoiceSelect(tempSelectedVoice)
    setOpen(false)
  }

  const handleVoiceClick = (voiceId: string) => {
    setTempSelectedVoice(voiceId)
  }

  const handlePlayVoice = (voice: ElevenLabsVoice, event: React.MouseEvent) => {
    event.stopPropagation()

    // Stop any currently playing audio
    if (audioElement) {
      audioElement.pause()
      audioElement.currentTime = 0
    }

    if (playingVoiceId === voice.voice_id) {
      // Stop playing current voice
      setPlayingVoiceId(null)
      setAudioElement(null)
    } else {
      // Start playing new voice
      setPlayingVoiceId(voice.voice_id)

      // Play the preview if available
      if (voice.preview_url) {
        const audio = new Audio(voice.preview_url)
        audio.onended = () => {
          setPlayingVoiceId(null)
          setAudioElement(null)
        }
        audio.play().catch(err => {
          console.error('Error playing voice preview:', err)
          setPlayingVoiceId(null)
          setAudioElement(null)
        })
        setAudioElement(audio)
      } else {
        // If no preview URL, reset after a short delay
        setTimeout(() => {
          setPlayingVoiceId(null)
          setAudioElement(null)
        }, 1000)
      }
    }
  }

  // Clean up audio when dialog closes
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen)

    if (!isOpen && audioElement) {
      audioElement.pause()
      audioElement.currentTime = 0
      setAudioElement(null)
      setPlayingVoiceId(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {triggerComponent || <Button variant="outline">Select Voice</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] text-zinc-900 dark:text-zinc-50">
        <DialogHeader>
          <DialogTitle>Select a voice</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col h-[60vh]">
          <Input
            placeholder="Search for a voice..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="mb-4"
          />

          <div className="overflow-y-auto flex-1 pr-2">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Spinner className="h-8 w-8" />
                <span className="ml-2">Loading voices...</span>
              </div>
            ) : error ? (
              <div className="rounded-md bg-red-50 p-4 dark:bg-red-900/20">
                <p className="text-sm text-red-800 dark:text-red-200">
                  Error loading voices. Please try again later.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedVoices).map(([category, categoryVoices]) => (
                  <div key={category} className="space-y-2">
                    <h3 className="text-sm font-medium text-zinc-500 dark:text-zinc-400">
                      {category}
                    </h3>
                    <div className="space-y-1">
                      {categoryVoices.map(voice => (
                        <div
                          key={voice.voice_id}
                          className={`flex items-center justify-between p-2 rounded-md cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-800 ${
                            tempSelectedVoice === voice.voice_id ? 'bg-zinc-100 dark:bg-zinc-800' : ''
                          }`}
                          onClick={() => handleVoiceClick(voice.voice_id)}
                        >
                          <div className="flex items-center gap-3">
                            <button
                              type="button"
                              className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary hover:bg-primary/20"
                              onClick={(e) => handlePlayVoice(voice, e)}
                              aria-label={playingVoiceId === voice.voice_id ? "Pause voice" : "Play voice"}
                            >
                              {playingVoiceId === voice.voice_id ?
                                <Pause className="h-4 w-4" /> :
                                <Play className="h-4 w-4" />}
                            </button>
                            <div>
                              <p className="text-sm font-medium">{voice.name}</p>
                              <div className="flex gap-2 mt-1">
                                {voice.labels?.accent && (
                                  <Badge variant="outline" className="text-xs">
                                    {voice.labels.accent}
                                  </Badge>
                                )}
                                {voice.labels?.language && (
                                  <Badge variant="outline" className="text-xs">
                                    {voice.labels.language}
                                  </Badge>
                                )}
                                {voice.labels?.use_case && (
                                  <Badge variant="outline" className="text-xs">
                                    {voice.labels.use_case}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          {tempSelectedVoice === voice.voice_id && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-4 border-t pt-4">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm}>
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
