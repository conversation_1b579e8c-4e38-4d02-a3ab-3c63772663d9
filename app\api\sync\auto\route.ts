import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import * as elevenLabsSync from '@/utils/sync/elevenlabs-sync';

/**
 * Automatically synchronizes all agents for an organization
 */
export async function POST(request: NextRequest) {
  try {
    const { organizationId } = await request.json();

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verify if the organization exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id')
      .eq('id', organizationId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Synchronize all agents for the organization
    const result = await elevenLabsSync.syncOrganizationAgents(supabase, organizationId);

    return NextResponse.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error in auto-sync:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Gets synchronization status for an organization
 */
export async function GET(request: NextRequest) {
  const organizationId = request.nextUrl.searchParams.get('organizationId');

  if (!organizationId) {
    return NextResponse.json(
      { error: 'Organization ID is required' },
      { status: 400 }
    );
  }

  try {
    const supabase = await createClient();

    // Get all agents from Supabase
    const supabaseAgents = await elevenLabsSync.getOrganizationAgents(supabase, organizationId);

    // Filter out agents without configs (all agents should have configs now)
    const agentsWithConfigs = supabaseAgents.filter(agent => {
      return agent.config && typeof agent.config === 'object';
    });

    // Get all agents from ElevenLabs
    const elevenLabsAgents = await elevenLabsSync.getElevenLabsAgentsForOrg(organizationId);

    // Map agents
    const mappings = elevenLabsSync.mapAgents(agentsWithConfigs, elevenLabsAgents, organizationId);

    // Calculate statistics
    const stats = {
      totalSupabaseAgents: supabaseAgents.length,
      agentsWithConfigs: agentsWithConfigs.length,
      totalElevenLabsAgents: elevenLabsAgents.length,
      mappedAgents: mappings.length,
      unmappedSupabaseAgents: agentsWithConfigs.length - mappings.length,
      orphanedElevenLabsAgents: elevenLabsAgents.length - mappings.length,
      syncPercentage: agentsWithConfigs.length > 0
        ? Math.round((mappings.length / agentsWithConfigs.length) * 100)
        : 100
    };

    return NextResponse.json({
      success: true,
      stats,
      needsSync: stats.unmappedSupabaseAgents > 0 || stats.orphanedElevenLabsAgents > 0
    });
  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

