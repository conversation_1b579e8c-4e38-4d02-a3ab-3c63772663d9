import { getOrganizationAgents } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'
import { AgentsTable } from '@/components/agents/agents-table'
import { AutoSync } from './auto-sync'

interface PageProps  {
  params: Promise<{
    orgId: string,
  }>,
  searchParams?: Promise<{
    team?: string,
    search?: string,
    sort?: string,
    direction?: string,
    creator?: string,
    status?: string,
    language?: string
  }>
}


export default async function Page({ params, searchParams }: PageProps) {
  const { orgId } = await params;
  const awaitedSearchParams = await searchParams;
  const teamFilter = awaitedSearchParams?.team;
  const pageSize = 100; // Get more agents to display in the table

  const supabase = await createClient();
  const {data:agents, error} = await getOrganizationAgents(
    supabase,
    orgId,
    '',  // No search filter initially
    1,   // First page
    pageSize
  );

  if (error) {
    return <div className="p-4">Error fetching agents</div>
  }

  return (
    <div className="p-6">
      {/* Add AutoSync component to automatically sync agents */}
      <AutoSync organizationId={orgId} />

      <AgentsTable
        agents={agents || []}
        orgId={orgId}
        initialTeamFilter={teamFilter}
      />
    </div>
  )
}