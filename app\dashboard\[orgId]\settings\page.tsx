import { PropsWithChildren } from 'react'
import { cn } from "@/utils/cn"
import { getOrganizationAgents } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'
import SearchList from '@/components/SearchList/search-list'
import { ListState, ListContext, BaseItem } from '@/types/list'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { AgentItem } from '@/components/agent-list-components'

interface PageProps  {
  params: Promise<{
    orgId: string,
  }>,
  searchParams?: Promise<{
    search?: string,
    page?: string
  }>
}


export default async function Page({ params, searchParams }: PageProps) {
  const { orgId } = await params;
  const awaitedSearchParams = await searchParams;
  const search = awaitedSearchParams?.search || '';
  const page = Number(awaitedSearchParams?.page) || 1;
  const pageSize = 10;

  const supabase = await createClient();
  const {data:agents, count, hasMore, error} = await getOrganizationAgents(
    supabase, 
    orgId,
    search,
    page,
    pageSize
  );
    if (error) {
        return <div className="p-4">Error fetching agents</div>
    }

    const initialState: ListState<BaseItem> = {
      items: agents || [],
      count: count || 0,
      hasMore: hasMore || false,
      search,
      page
    }

    const listContext: ListContext = {
      orgId: orgId,
      basePath: `/dashboard/${orgId}/agents`,
      currentPath: `/dashboard/${orgId}/agents`,
      resourceType: 'agent'
    }



    return (
      <div className="@container/main grid grid-cols-12 h-full relative">
        {/* Left column: Agent list */}
        <div className={cn(
          "border-r overflow-y-auto",
          "transition-[grid-column] duration-300 ease-in-out",
          "col-span-12 @xl/main:col-span-3"
        )}>
           <div className="flex items-center justify-between p-4 space-y-4">
              <h2 className="text-lg font-semibold">Agents</h2>
              <Button size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Agent
              </Button>
            </div>


          <SearchList<BaseItem>
            context={listContext}
            fetchUrl={`/api/organizations/${orgId}/agents`}
            initialState={initialState}
            searchPlaceholder="Search agents..."
            listTitle="Agents"
            slots={{
              itemContent: AgentItem
            }}
          />
        </div>

        {/* Right column: No agent selected message */}
        <div className={cn(
          "overflow-y-auto",
          "transition-[grid-column] duration-300 ease-in-out",
          "hidden @xl/main:block @xl/main:col-span-9"
        )}>
          <div className="p-6">
            <p className="text-muted-foreground">No agent selected</p>
          </div>
        </div>
      </div>
    )
}