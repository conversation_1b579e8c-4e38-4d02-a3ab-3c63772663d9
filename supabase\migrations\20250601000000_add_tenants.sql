-- Create tenants table
CREATE TABLE IF NOT EXISTS "public"."tenants" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "slug" "text" NOT NULL UNIQUE,
    "domain" "text" NOT NULL UNIQUE,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "updated_at" timestamp without time zone DEFAULT "now"(),
    "settings" "jsonb" DEFAULT '{}'::jsonb,
    "status" "text" DEFAULT 'active'::text,
    CONSTRAINT "tenants_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "tenants_status_check" CHECK (("status" = ANY (ARRAY['active'::text, 'inactive'::text, 'deleted'::text])))
);

-- Add tenant_id to organizations table
ALTER TABLE "public"."organizations"
ADD COLUMN IF NOT EXISTS "tenant_id" "uuid" REFERENCES "public"."tenants"("id") ON DELETE CASCADE;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS "idx_organizations_tenant_id" ON "public"."organizations" ("tenant_id");
CREATE INDEX IF NOT EXISTS "idx_tenants_slug" ON "public"."tenants" ("slug");
CREATE INDEX IF NOT EXISTS "idx_tenants_domain" ON "public"."tenants" ("domain");

-- Create tenant_admins table to track tenant administrators
CREATE TABLE IF NOT EXISTS "public"."tenant_admins" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "tenant_id" "uuid" REFERENCES "public"."tenants"("id") ON DELETE CASCADE,
    "user_id" "uuid" REFERENCES "public"."users"("id") ON DELETE CASCADE,
    "created_at" timestamp without time zone DEFAULT "now"(),
    CONSTRAINT "tenant_admins_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "tenant_admins_tenant_user_unique" UNIQUE ("tenant_id", "user_id")
);

-- Create function to check if a user is a tenant admin
CREATE OR REPLACE FUNCTION "public"."is_tenant_admin"("tenant_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.tenant_admins
    WHERE tenant_admins.tenant_id = $1
    AND tenant_admins.user_id = auth.uid()
  );
END;
$$;

-- Create function to create a new tenant
CREATE OR REPLACE FUNCTION "public"."create_tenant"("tenant_name" "text", "tenant_slug" "text", "tenant_domain" "text") RETURNS uuid
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_tenant_id uuid := gen_random_uuid();
BEGIN
  -- Create the tenant
  INSERT INTO public.tenants (id, name, slug, domain)
  VALUES (new_tenant_id, tenant_name, tenant_slug, tenant_domain);

  -- Add the current user as a tenant admin
  INSERT INTO public.tenant_admins (tenant_id, user_id)
  VALUES (new_tenant_id, auth.uid());

  RETURN new_tenant_id;
END;
$$;

-- Update the create_organization function to include tenant_id
CREATE OR REPLACE FUNCTION "public"."create_organization"("org_name" "text", "tenant_id" "uuid" DEFAULT NULL) RETURNS uuid
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_org_id uuid := gen_random_uuid();
BEGIN
  -- Create the organization
  INSERT INTO public.organizations (id, name, tenant_id)
  VALUES (new_org_id, org_name, tenant_id);

  -- Add the current user as an admin
  INSERT INTO public.organization_memberships (organization_id, user_id, role)
  VALUES (new_org_id, auth.uid(), 'admin');

  -- Create a wallet for the organization if needed
  INSERT INTO public.credit_wallets (organization_id, balance_cents)
  VALUES (new_org_id, 0);

  RETURN new_org_id;
END;
$$;

-- Update the handle_new_user function to work with tenants
CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
declare
  new_org_id uuid := gen_random_uuid();
begin
  -- Insert user
  insert into public.users (id, email, full_name, avatar_url)
  values (new.id, new.email, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');

  -- Create org (without tenant_id for now - will be assigned later if needed)
  insert into public.organizations (id, name)
  values (new_org_id, 'My Workspace');

  -- Add as admin
  insert into public.organization_memberships (organization_id, user_id, role)
  values (new_org_id, new.id, 'admin');

  -- Add wallet
  insert into public.credit_wallets (organization_id, balance_cents)
  values (new_org_id, 0);

  return new;
end;$$;

-- Create RLS policies for tenants
ALTER TABLE "public"."tenants" ENABLE ROW LEVEL SECURITY;

-- Tenant admins can view their tenants
CREATE POLICY "Tenant admins can view their tenants" ON "public"."tenants"
FOR SELECT USING (
  "public"."is_tenant_admin"(id)
);

-- Tenant admins can update their tenants
CREATE POLICY "Tenant admins can update their tenants" ON "public"."tenants"
FOR UPDATE USING (
  "public"."is_tenant_admin"(id)
);

-- Update organization policies to consider tenant_id
CREATE OR REPLACE FUNCTION "public"."is_member_or_tenant_admin"("org_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  tenant_id uuid;
BEGIN
  -- Get the tenant_id for this organization
  SELECT organizations.tenant_id INTO tenant_id
  FROM public.organizations
  WHERE organizations.id = org_id;

  -- Check if user is a member of the organization or a tenant admin
  RETURN EXISTS (
    SELECT 1
    FROM public.organization_memberships
    WHERE organization_memberships.organization_id = org_id
    AND organization_memberships.user_id = auth.uid()
  ) OR (
    tenant_id IS NOT NULL AND public.is_tenant_admin(tenant_id)
  );
END;
$$;

-- Update the existing policy or create a new one
DROP POLICY IF EXISTS "Members can view organizations" ON "public"."organizations";
CREATE POLICY "Members or tenant admins can view organizations" ON "public"."organizations"
FOR SELECT USING (
  "public"."is_member_or_tenant_admin"(id)
);
