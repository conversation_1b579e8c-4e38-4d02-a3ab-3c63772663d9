'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/Toasts/use-toast'
import { Shield, User, Circle } from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface UserRoleFormProps {
  userId: string
  orgId: string
  currentRole: string
}

export function UserRoleForm({ userId, orgId, currentRole }: UserRoleFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [role, setRole] = useState(currentRole)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  async function onSubmit() {
    if (role === currentRole) {
      toast({
        title: 'No changes',
        description: 'The role has not been changed.',
      })
      return
    }

    setShowConfirmDialog(true)
  }

  async function confirmRoleChange() {
    setIsSubmitting(true)

    try {
      // In a real implementation, you would update the user role
      // For now, we'll just show a success message

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: 'Role updated',
        description: `User role has been updated to ${role}.`,
      })

      // Refresh the page to show the updated data
      router.refresh()
    } catch (error) {
      console.error('Error updating role:', error)
      toast({
        title: 'Error',
        description: 'Failed to update role. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
      setShowConfirmDialog(false)
    }
  }

  const handleRoleChange = (newRole: string) => {
    setRole(newRole)
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">User Role</h3>
        <div className="space-y-4">
          <div className="flex items-start space-x-3 space-y-0">
            <div
              className="flex h-4 w-4 shrink-0 rounded-full border border-zinc-900 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-zinc-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-zinc-50 dark:ring-offset-zinc-950 dark:focus-visible:ring-zinc-300 cursor-pointer"
              onClick={() => handleRoleChange('admin')}
            >
              {role === 'admin' && (
                <div className="flex items-center justify-center">
                  <Circle className="h-2.5 w-2.5 fill-current text-current" />
                </div>
              )}
            </div>
            <div className="grid gap-1.5 leading-none">
              <div className="flex items-center gap-2">
                <Label
                  htmlFor="admin"
                  className="font-medium cursor-pointer"
                  onClick={() => handleRoleChange('admin')}
                >
                  Admin
                </Label>
                <Shield className="h-4 w-4 text-primary" />
              </div>
              <p className="text-sm text-muted-foreground">
                Full access to manage users, teams, and organization settings
              </p>
            </div>
          </div>
          <div className="flex items-start space-x-3 space-y-0">
            <div
              className="flex h-4 w-4 shrink-0 rounded-full border border-zinc-900 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-zinc-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-zinc-50 dark:ring-offset-zinc-950 dark:focus-visible:ring-zinc-300 cursor-pointer"
              onClick={() => handleRoleChange('member')}
            >
              {role === 'member' && (
                <div className="flex items-center justify-center">
                  <Circle className="h-2.5 w-2.5 fill-current text-current" />
                </div>
              )}
            </div>
            <div className="grid gap-1.5 leading-none">
              <div className="flex items-center gap-2">
                <Label
                  htmlFor="member"
                  className="font-medium cursor-pointer"
                  onClick={() => handleRoleChange('member')}
                >
                  Member
                </Label>
                <User className="h-4 w-4 text-primary" />
              </div>
              <p className="text-sm text-muted-foreground">
                Can use agents but cannot manage users or organization settings
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          type="button"
          onClick={onSubmit}
          disabled={isSubmitting || role === currentRole}
        >
          {isSubmitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change user role?</DialogTitle>
            <DialogDescription>
              {role === 'admin'
                ? 'This will grant the user admin privileges, allowing them to manage users, teams, and organization settings.'
                : 'This will remove admin privileges from the user. They will no longer be able to manage users, teams, or organization settings.'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={confirmRoleChange} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="border-t pt-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Danger Zone</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium">Remove User</p>
              <p className="text-xs text-muted-foreground">
                Remove this user from the organization
              </p>
            </div>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => {
                // In a real implementation, you would show a confirmation dialog
                // and then remove the user from the organization
                alert('This would remove the user from the organization')
              }}
            >
              Remove
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
