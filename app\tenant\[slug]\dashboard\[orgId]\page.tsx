import { getTenantContext } from '@/utils/tenant/context';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';

interface TenantDashboardPageProps {
  params: Promise<{ slug: string; orgId: string }>;
}

export default async function TenantDashboardPage({ params }: TenantDashboardPageProps) {
  const { slug, orgId } = await params;
  const { tenant } = await getTenantContext(slug);
  
  if (!tenant) {
    return redirect('/');
  }
  
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return redirect(`/tenant/${slug}/signin`);
  }
  
  // Get organization details
  const { data: organization } = await supabase
    .from('organizations')
    .select('name')
    .eq('id', orgId)
    .single();
  
  // Get agent count
  const { count: agentCount } = await supabase
    .from('agents')
    .select('id', { count: 'exact', head: true })
    .eq('organization_id', orgId);
  
  // Get team count
  const { count: teamCount } = await supabase
    .from('agent_teams')
    .select('id', { count: 'exact', head: true })
    .eq('organization_id', orgId);
  
  // Get recent conversations
  const { data: recentConversations } = await supabase
    .from('conversations')
    .select(`
      id,
      start_time,
      duration_secs,
      cost_cents,
      agent:agent_id (name)
    `)
    .eq('agent.organization_id', orgId)
    .order('start_time', { ascending: false })
    .limit(5);
  
  return (
    <div className="container py-6">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white dark:bg-zinc-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-medium mb-2">Agents</h2>
          <p className="text-3xl font-bold">{agentCount || 0}</p>
        </div>
        
        <div className="bg-white dark:bg-zinc-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-medium mb-2">Teams</h2>
          <p className="text-3xl font-bold">{teamCount || 0}</p>
        </div>
        
        <div className="bg-white dark:bg-zinc-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-medium mb-2">Organization</h2>
          <p className="text-xl font-medium">{organization?.name || 'Loading...'}</p>
        </div>
      </div>
      
      <div className="bg-white dark:bg-zinc-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-4">Recent Conversations</h2>
        
        {recentConversations && recentConversations.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b dark:border-zinc-700">
                  <th className="text-left py-3 px-4">Agent</th>
                  <th className="text-left py-3 px-4">Date</th>
                  <th className="text-left py-3 px-4">Duration</th>
                  <th className="text-left py-3 px-4">Cost</th>
                </tr>
              </thead>
              <tbody>
                {recentConversations.map((conv) => (
                  <tr key={conv.id} className="border-b dark:border-zinc-700">
                    <td className="py-3 px-4">{conv.agent?.name || 'Unknown'}</td>
                    <td className="py-3 px-4">
                      {conv.start_time ? new Date(conv.start_time).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="py-3 px-4">
                      {conv.duration_secs ? `${Math.round(conv.duration_secs)}s` : 'N/A'}
                    </td>
                    <td className="py-3 px-4">
                      {conv.cost_cents ? `$${(conv.cost_cents / 100).toFixed(2)}` : 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-zinc-500 dark:text-zinc-400">No recent conversations found.</p>
        )}
      </div>
    </div>
  );
}
