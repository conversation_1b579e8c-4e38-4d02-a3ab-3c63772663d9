'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { AddAgentPanel } from './add-agent-modal'

interface AddAgentButtonProps {
  orgId: string
}

export function AddAgentButton({ orgId }: AddAgentButtonProps) {
  const [showPanel, setShowPanel] = useState(false)

  return (
    <>
      <Button size="sm" variant="outline" onClick={() => setShowPanel(true)}>
        <Plus className="h-4 w-4 mr-2" />
        Add Agent
      </Button>
      <AddAgentPanel 
        open={showPanel}
        onOpenChange={setShowPanel}
        orgId={orgId}
      />
    </>
  )
}