'use server';

import { createClient } from '@/utils/supabase/server';
import { Tenant, TenantContext } from '@/types/tenant';
import { cookies } from 'next/headers';

// Get tenant by slug
export async function getTenantBySlug(slug: string): Promise<Tenant | null> {
  console.log(`Looking up tenant by slug: ${slug}`);
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('tenants')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'active')
      .single();

    if (error) {
      console.log(`Error looking up tenant by slug: ${error.message}`);

      // Check if this is a "no rows returned" error
      if (error.code === 'PGRST116') {
        console.log(`No tenant found with slug: ${slug}`);
      }
      return null;
    }

    if (!data) {
      console.log(`No tenant data found for slug: ${slug}`);
      return null;
    }

    console.log(`Found tenant: ${data.name} (${data.id})`);
    return data as Tenant;
  } catch (error) {
    console.error(`Unexpected error in getTenantBySlug:`, error);
    return null;
  }
}

// Get tenant by domain
export async function getTenantByDomain(domain: string): Promise<Tenant | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('tenants')
    .select('*')
    .eq('domain', domain)
    .eq('status', 'active')
    .single();

  if (error || !data) {
    return null;
  }

  return data as Tenant;
}

// Check if user is a tenant admin
export async function isTenantAdmin(tenantId: string): Promise<boolean> {
  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return false;
  }

  // Check if user is a tenant admin
  const { data, error } = await supabase
    .from('tenant_admins')
    .select('id')
    .eq('tenant_id', tenantId)
    .eq('user_id', user.id)
    .single();

  return !error && !!data;
}

// Get tenant context
export async function getTenantContext(slug: string): Promise<TenantContext> {
  console.log(`Getting tenant context for slug: ${slug}`);

  try {
    const tenant = await getTenantBySlug(slug);

    if (!tenant) {
      console.log(`No tenant found for slug: ${slug}, returning null context`);
      return { tenant: null, isAdmin: false };
    }

    console.log(`Found tenant: ${tenant.name}, checking admin status`);
    const isAdmin = await isTenantAdmin(tenant.id);
    console.log(`User is${isAdmin ? '' : ' not'} an admin for tenant: ${tenant.name}`);

    return { tenant, isAdmin };
  } catch (error) {
    console.error(`Error in getTenantContext:`, error);
    return { tenant: null, isAdmin: false };
  }
}

// Add user to tenant and create organization if needed
export async function addUserToTenant(userId: string, tenantId: string, isPrimary: boolean = false): Promise<string> {
  const supabase = await createClient();

  // Call the function to add user to tenant and create organization
  const { data: orgId, error } = await supabase
    .rpc('add_user_to_tenant', {
      user_id: userId,
      tenant_id: tenantId,
      is_primary: isPrimary
    });

  if (error) {
    console.error('Error adding user to tenant:', error);
    throw new Error(`Failed to add user to tenant: ${error.message}`);
  }

  return orgId;
}

// Check if user belongs to tenant
export async function checkUserTenantAccess(userId: string, tenantId: string): Promise<boolean> {
  const supabase = await createClient();

  // Check if user belongs to this tenant
  const { data, error } = await supabase
    .from('tenant_users')
    .select('id')
    .eq('user_id', userId)
    .eq('tenant_id', tenantId)
    .single();

  return !error && !!data;
}

// Get all tenants for a user
export async function getUserTenants(userId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('tenant_users')
    .select(`
      tenant:tenant_id (
        id,
        name,
        slug,
        domain,
        status
      ),
      is_primary
    `)
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching user tenants:', error);
    return [];
  }

  return data.map(item => ({
    ...item.tenant,
    isPrimary: item.is_primary
  }));
}

// Get primary tenant for a user
export async function getUserPrimaryTenant(userId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('tenant_users')
    .select(`
      tenant:tenant_id (
        id,
        name,
        slug,
        domain,
        status
      )
    `)
    .eq('user_id', userId)
    .eq('is_primary', true)
    .single();

  if (error || !data) {
    return null;
  }

  return data.tenant;
}

// Get organizations for tenant
export async function getTenantOrganizations(tenantId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('organizations')
    .select('*')
    .eq('tenant_id', tenantId);

  if (error) {
    return [];
  }

  return data;
}

// Create a new tenant
export async function createTenant(name: string, slug: string, domain: string) {
  const supabase = await createClient();

  // Check if slug is available
  const { data: existingSlug } = await supabase
    .from('tenants')
    .select('id')
    .eq('slug', slug)
    .single();

  if (existingSlug) {
    throw new Error('Tenant slug already exists');
  }

  // Check if domain is available
  const { data: existingDomain } = await supabase
    .from('tenants')
    .select('id')
    .eq('domain', domain)
    .single();

  if (existingDomain) {
    throw new Error('Domain already exists');
  }

  // Create tenant
  const { data: tenantId, error } = await supabase
    .rpc('create_tenant', { tenant_name: name, tenant_slug: slug, tenant_domain: domain });

  if (error) {
    throw new Error(`Failed to create tenant: ${error.message}`);
  }

  return tenantId;
}
