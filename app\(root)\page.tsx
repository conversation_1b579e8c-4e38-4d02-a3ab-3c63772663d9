import React from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { createClient } from '@/utils/supabase/server';
import { Bot, Users, MessageSquare, Database, Shield, BarChart, ArrowRight } from 'lucide-react'
import { HomeWidgetPreview } from '@/components/home/<USER>'

const HomePage = async () => {
  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user } } = await supabase.auth.getUser();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-zinc-50 dark:bg-zinc-900">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="flex flex-col space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  AI-Powered Conversational Agents for Your Business
                </h1>
                <p className="max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
                  Create, manage, and deploy intelligent voice agents that engage with your customers naturally and effectively.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                {user ? (
                  <Link href="/dashboard">
                    <Button size="lg">Go to Dashboard <ArrowRight className="ml-2 h-4 w-4" /></Button>
                  </Link>
                ) : (
                  <>
                    <Link href="/signin">
                      <Button size="lg">Get Started <ArrowRight className="ml-2 h-4 w-4" /></Button>
                    </Link>
                    <Link href="/signin">
                      <Button variant="outline" size="lg">Sign In</Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
            <div className="flex justify-center">
              <HomeWidgetPreview />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-12 md:py-24 lg:py-32">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Powerful Features
              </h2>
              <p className="mx-auto max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
                Everything you need to create and manage AI-powered conversational agents
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-8">
            <Card>
              <CardHeader>
                <Bot className="h-10 w-10 mb-2 text-primary" />
                <CardTitle>AI Agents</CardTitle>
                <CardDescription>
                  Create customizable AI agents with different personalities and capabilities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Configure voice settings, language preferences, and advanced parameters to create the perfect agent for your business needs.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Users className="h-10 w-10 mb-2 text-primary" />
                <CardTitle>Team Management</CardTitle>
                <CardDescription>
                  Organize agents into teams with shared budgets and settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Manage access, track usage, and allocate resources efficiently across your organization.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <MessageSquare className="h-10 w-10 mb-2 text-primary" />
                <CardTitle>Conversation History</CardTitle>
                <CardDescription>
                  Track and analyze all conversations with your agents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Review transcripts, analyze performance metrics, and gain insights to improve your agents over time.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Database className="h-10 w-10 mb-2 text-primary" />
                <CardTitle>Knowledge Base</CardTitle>
                <CardDescription>
                  Equip your agents with custom knowledge
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Upload documents and data to give your agents the specific information they need to assist your customers.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <BarChart className="h-10 w-10 mb-2 text-primary" />
                <CardTitle>Analytics Dashboard</CardTitle>
                <CardDescription>
                  Comprehensive usage and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Track call volumes, durations, costs, and other key metrics to optimize your agent deployment.</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Shield className="h-10 w-10 mb-2 text-primary" />
                <CardTitle>Budget Controls</CardTitle>
                <CardDescription>
                  Set and manage budgets for teams and agents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Control costs with flexible budget settings and receive alerts when approaching limits.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-zinc-50 dark:bg-zinc-900">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                How It Works
              </h2>
              <p className="mx-auto max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
                Get started with BotCom AI in just a few simple steps
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-3 mt-8">
            <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-white dark:bg-zinc-950">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white">
                1
              </div>
              <h3 className="text-xl font-bold">Create Your Agent</h3>
              <p className="text-center text-zinc-500 dark:text-zinc-400">
                Define your agent's personality, voice, and knowledge base to match your brand.
              </p>
            </div>
            <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-white dark:bg-zinc-950">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white">
                2
              </div>
              <h3 className="text-xl font-bold">Configure Settings</h3>
              <p className="text-center text-zinc-500 dark:text-zinc-400">
                Customize language preferences, response styles, and integration options.
              </p>
            </div>
            <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-white dark:bg-zinc-950">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white">
                3
              </div>
              <h3 className="text-xl font-bold">Deploy & Monitor</h3>
              <p className="text-center text-zinc-500 dark:text-zinc-400">
                Launch your agent and track performance through our comprehensive dashboard.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-white dark:bg-zinc-950">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Trusted by Businesses
              </h2>
              <p className="mx-auto max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
                See what our customers are saying about BotCom AI
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 mt-8">
            <Card className="p-6">
              <div className="flex flex-col space-y-4">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 fill-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
                <p className="text-zinc-500 dark:text-zinc-400">
                  "BotCom AI has transformed our customer service. Our AI agents handle routine inquiries 24/7, freeing our team to focus on complex issues."
                </p>
                <div>
                  <p className="font-semibold">Sarah Johnson</p>
                  <p className="text-sm text-zinc-500 dark:text-zinc-400">Customer Service Director, TechCorp</p>
                </div>
              </div>
            </Card>
            <Card className="p-6">
              <div className="flex flex-col space-y-4">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 fill-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
                <p className="text-zinc-500 dark:text-zinc-400">
                  "The analytics dashboard gives us incredible insights into customer interactions. We've been able to optimize our agents and improve satisfaction rates by 35%."
                </p>
                <div>
                  <p className="font-semibold">Michael Chen</p>
                  <p className="text-sm text-zinc-500 dark:text-zinc-400">Operations Manager, Global Retail</p>
                </div>
              </div>
            </Card>
            <Card className="p-6">
              <div className="flex flex-col space-y-4">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 fill-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
                <p className="text-zinc-500 dark:text-zinc-400">
                  "Setting up our first AI agent was surprisingly easy. The team management features help us organize our agents by department and track budgets effectively."
                </p>
                <div>
                  <p className="font-semibold">Emily Rodriguez</p>
                  <p className="text-sm text-zinc-500 dark:text-zinc-400">IT Director, FinanceHub</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 border-t">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Ready to Transform Your Customer Interactions?
              </h2>
              <p className="mx-auto max-w-[700px] text-zinc-500 md:text-xl dark:text-zinc-400">
                Join businesses that are leveraging AI to provide exceptional customer experiences.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              {user ? (
                <Link href="/dashboard">
                  <Button size="lg">Go to Dashboard <ArrowRight className="ml-2 h-4 w-4" /></Button>
                </Link>
              ) : (
                <Link href="/signin">
                  <Button size="lg">Get Started Today <ArrowRight className="ml-2 h-4 w-4" /></Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default HomePage