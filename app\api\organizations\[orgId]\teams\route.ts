import { NextResponse } from 'next/server'
import { getOrganizationTeams } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    // Initialize Supabase client
    const supabase = await createClient()
    const {orgId} = await params;

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Verify organization access
    const { data: membership } = await supabase
      .from('organization_memberships')
      .select('organization_id')
      .eq('user_id', user.id)
      .eq('organization_id', orgId)
      .single()

    if (!membership) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')

    // Fetch teams using the existing query
    const { data, count, error, hasMore } = await getOrganizationTeams(
      supabase,
      orgId,
      search,
      page,
      pageSize
    )

    if (error) {
      return new NextResponse('Internal Server Error', { status: 500 })
    }

    // Return success response
    return NextResponse.json({
      data,
      count,
      hasMore,
      error: null
    })

  } catch (error) {
    console.error('Error in teams route:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    const { name, budget } = await request.json()
    const { orgId } = await params;

    // Initialize Supabase client
    const supabase = await createClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Verify organization access
    const { data: membership } = await supabase
      .from('organization_memberships')
      .select('organization_id')
      .eq('user_id', user.id)
      .eq('organization_id', orgId)
      .single()

    if (!membership) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    // Create the team with budget if provided
    const teamData = {
      name,
      organization_id: orgId,
    };

    // Add budget if provided
    if (budget !== undefined) {
      // Convert dollars to cents and store in budget_cents
      // @ts-ignore - Add budget_cents field
      teamData.budget_cents = Math.round(budget * 100);
    }

    const { data, error } = await supabase
      .from('agent_teams')
      .insert([teamData])
      .select()
      .single()

    if (error) {
      console.error('Error creating team:', error)
      return new NextResponse('Internal Server Error', { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in teams route:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
