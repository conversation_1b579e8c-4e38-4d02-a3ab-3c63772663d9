-- Function to find orphaned organizations (for admin use)
CREATE OR REPLACE FUNCTION public.find_orphaned_organizations()
RETURNS TABLE (id UUID, name TEXT, created_at TIMESTAMP)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT o.id, o.name, o.created_at
  FROM organizations o
  LEFT JOIN organization_memberships m ON o.id = m.organization_id
  WHERE m.id IS NULL;
$$;

-- Function to check if the current user is an admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM organization_memberships
    WHERE user_id = auth.uid() AND role = 'admin'
  );
$$;

-- Add comments to explain the purpose of these functions
COMMENT ON FUNCTION public.find_orphaned_organizations() IS 'Finds organizations that have no members';
COMMENT ON FUNCTION public.is_admin() IS 'Checks if the current user is an admin of any organization';