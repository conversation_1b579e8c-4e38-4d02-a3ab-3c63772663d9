# Orphaned Organizations Handling

This document describes how the system handles orphaned organizations (organizations with no members).

## Problem

When the last user of an organization deletes their account or is removed from the organization, the organization becomes "orphaned" - it continues to exist in the database with all its associated data, but has no members who can access it.

Without proper handling, these orphaned organizations would accumulate in the database over time, taking up space and potentially causing confusion.

## Solution

We've implemented an automatic cleanup mechanism using PostgreSQL triggers and functions that:

1. Detects when an organization becomes orphaned (has no members)
2. Automatically deletes the orphaned organization and all its associated data

## Implementation Details

### Database Function

The `delete_orphaned_organization()` function:
- Is triggered after a membership is deleted
- Checks if the organization has any remaining members
- If no members remain, deletes the organization

### Cascade Deletion

When an organization is deleted, all related data is automatically deleted due to the CASCADE constraints on foreign keys:

- Agents
- Teams
- Credit wallets
- Credit transactions
- Live sessions
- Conversations
- Other related data

### Migration

The implementation is included in the migration file:
`supabase/migrations/20250501000000_delete_orphaned_orgs.sql`

## Testing

A test script is provided to verify the functionality:
`scripts/test_orphaned_orgs.js`

### Prerequisites

Before running the test, make sure you have:

1. Node.js installed
2. Required npm packages installed:
   ```bash
   npm install @supabase/supabase-js dotenv
   ```
3. A `.env.local` file in the root directory with the following variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

### Running the Test

To run the test:
```bash
node scripts/test_orphaned_orgs.js
```

See `scripts/README.md` for more detailed instructions and troubleshooting.

## Considerations

- This approach ensures that no orphaned organizations remain in the database
- All data associated with an orphaned organization is permanently deleted
- If data retention is required, consider implementing an archiving mechanism instead of deletion
