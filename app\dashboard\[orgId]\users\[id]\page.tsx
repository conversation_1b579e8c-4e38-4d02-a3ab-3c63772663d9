import { notFound } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { UserProfileForm } from '@/components/users/user-profile-form'
import { UserRoleForm } from '@/components/users/user-role-form'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface PageProps {
  params: Promise<{ orgId: string, id: string }>
}

export default async function Page({ params }: PageProps) {
  const { orgId, id } = await params

  // Fetch user details
  const supabase = await createClient()

  // Get user details
  const { data: membership, error: membershipError } = await supabase
    .from('organization_memberships')
    .select(`
      *,
      user:user_id (id, email, full_name, avatar_url, created_at, updated_at)
    `)
    .eq('organization_id', orgId)
    .eq('user_id', id)
    .single()

  if (membershipError || !membership) {
    console.error('Error fetching user:', membershipError)
    return notFound()
  }

  const user = membership.user
  const role = membership.role

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link href={`/dashboard/${orgId}/users`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{user?.full_name || user?.email}</h1>
          <p className="text-muted-foreground">{user?.email}</p>
        </div>
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="role">Role & Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Profile</CardTitle>
              <CardDescription>
                View and update user profile information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserProfileForm
                user={user!}
                orgId={orgId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="role" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Role & Permissions</CardTitle>
              <CardDescription>
                Manage user role and access permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserRoleForm
                userId={id}
                orgId={orgId}
                currentRole={role || 'member'}
              />
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <p className="text-sm text-muted-foreground">
                <strong>Admin:</strong> Can manage users, teams, and all settings.<br />
                <strong>Member:</strong> Can use agents but cannot manage users or organization settings.
              </p>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
