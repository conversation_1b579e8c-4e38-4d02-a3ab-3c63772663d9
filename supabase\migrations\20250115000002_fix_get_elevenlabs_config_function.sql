-- Fix the get_elevenlabs_config function to work with the new unified agents table structure
-- This function now takes an agent_id instead of config_id and gets config from agents.config

-- First drop the old function
DROP FUNCTION IF EXISTS "public"."get_elevenlabs_config"("config_id" "uuid");

-- Create the new function with agent_id parameter
CREATE OR REPLACE FUNCTION "public"."get_elevenlabs_config"("agent_id" "uuid") RETURNS "jsonb"
    LANGUAGE "sql" STABLE
    AS $$
  SELECT jsonb_build_object(
    'name', agents.name,
    'conversation_config', jsonb_build_object(
      'agent', jsonb_build_object(
        'language', agents.config->>'language',
        'prompt', agents.config->'prompt_config',
        'first_message', agents.config->>'first_message',
        'dynamic_variables', agents.config->'dynamic_variables'
      ),
      'asr', agents.config->'asr_config',
      'tts', agents.config->'tts_config',
      'turn', agents.config->'turn_config',
      'conversation', agents.config->'conversation_config',
      'language_presets', '{}',
      'is_blocked_ivc', false,
      'is_blocked_non_ivc', false
    ),
    'platform_settings', agents.config->'platform_settings'
  )
  FROM agents
  WHERE agents.id = agent_id;
$$;

-- Grant permissions
GRANT ALL ON FUNCTION "public"."get_elevenlabs_config"("agent_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_elevenlabs_config"("agent_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_elevenlabs_config"("agent_id" "uuid") TO "service_role";
