import { SupabaseClient } from '@supabase/supabase-js';
import * as elevenlabsQueries from '@/utils/elevenlabs/queries';

/**
 * Represents the synchronization state between Supabase and ElevenLabs
 */
export interface SyncState {
  supabaseSuccess: boolean;
  elevenLabsSuccess: boolean;
  supabaseError?: Error;
  elevenLabsError?: Error;
  elevenLabsAgentId?: string;
}

/**
 * Represents an agent mapping between Supabase and ElevenLabs
 */
export interface AgentMapping {
  supabaseAgentId: string;
  elevenLabsAgentId: string;
  name: string;
  organizationId: string;
}

/**
 * Formats an agent name for ElevenLabs with organization ID
 */
export function formatElevenLabsAgentName(agentName: string, orgId: string, agentId: string): string {
  return `${agentName} [SB-${orgId}-${agentId}]`;
}

/**
 * Extracts Supabase agent ID from an ElevenLabs agent name
 * Returns null if the name doesn't follow the expected format
 */
export function extractAgentIdFromName(name: string): string | null {
  const match = name.match(/\[SB-([^-]+)-([^\]]+)\]/);
  if (match && match[2]) {
    return match[2];
  }
  return null;
}

/**
 * Creates an agent in ElevenLabs and updates the Supabase record with the ElevenLabs agent ID
 */
export async function createElevenLabsAgent(
  supabase: SupabaseClient,
  agentId: string,
  agentName: string,
  orgId: string,
  conversationConfig: any
): Promise<SyncState> {
  const state: SyncState = {
    supabaseSuccess: false,
    elevenLabsSuccess: false
  };

  try {
    // Format the name for ElevenLabs
    const formattedName = formatElevenLabsAgentName(agentName, orgId, agentId);

    // Create the agent in ElevenLabs
    const elevenLabsAgent = await elevenlabsQueries.createAgent(
      formattedName,
      conversationConfig
    );

    if (elevenLabsAgent && elevenLabsAgent.agent_id) {
      state.elevenLabsSuccess = true;
      state.elevenLabsAgentId = elevenLabsAgent.agent_id;

      // Get the agent config (now stored in the agents table)
      const { data: agent } = await supabase
        .from('agents')
        .select('*')
        .eq('id', agentId)
        .single();

      if (agent) {
        // Update the agent with the ElevenLabs agent ID
        const { error } = await supabase
          .from('agents')
          .update({
            external_id: elevenLabsAgent.agent_id,
            type: 'elevenlabs'
          })
          .eq('id', agentId);

        if (!error) {
          state.supabaseSuccess = true;
        } else {
          state.supabaseError = new Error(error.message);
          // Rollback: Delete the ElevenLabs agent since we couldn't update Supabase
          try {
            await elevenlabsQueries.deleteAgent(elevenLabsAgent.agent_id);
          } catch (rollbackError) {
            console.error('Error rolling back ElevenLabs agent creation:', rollbackError);
          }
        }
      }
    }
  } catch (error) {
    state.elevenLabsError = error instanceof Error ? error : new Error(String(error));
  }

  return state;
}

/**
 * Deletes an agent from ElevenLabs
 */
export async function deleteElevenLabsAgent(
  elevenLabsAgentId: string
): Promise<SyncState> {
  const state: SyncState = {
    supabaseSuccess: true, // Supabase deletion is handled separately
    elevenLabsSuccess: false
  };

  try {
    await elevenlabsQueries.deleteAgent(elevenLabsAgentId);
    state.elevenLabsSuccess = true;
  } catch (error) {
    state.elevenLabsError = error instanceof Error ? error : new Error(String(error));
  }

  return state;
}

/**
 * Updates an agent in ElevenLabs
 */
export async function updateElevenLabsAgent(
  elevenLabsAgentId: string,
  agentName: string,
  orgId: string,
  conversationConfig: any
): Promise<SyncState> {
  const state: SyncState = {
    supabaseSuccess: true, // Supabase update is handled separately
    elevenLabsSuccess: false
  };

  try {
    // Format the name for ElevenLabs
    const formattedName = `${agentName} [SB-${orgId}]`;

    // Update the agent in ElevenLabs
    await elevenlabsQueries.updateAgent(
      elevenLabsAgentId,
      formattedName,
      conversationConfig
    );

    state.elevenLabsSuccess = true;
  } catch (error) {
    state.elevenLabsError = error instanceof Error ? error : new Error(String(error));
  }

  return state;
}

/**
 * Checks if an ElevenLabs agent exists and is valid
 */
export async function verifyElevenLabsAgent(
  elevenLabsAgentId: string
): Promise<boolean> {
  try {
    const agent = await elevenlabsQueries.getAgent(elevenLabsAgentId);
    return !!agent;
  } catch (error) {
    return false;
  }
}

/**
 * Repairs synchronization by creating a missing ElevenLabs agent
 */
export async function repairElevenLabsSync(
  supabase: SupabaseClient,
  agentId: string
): Promise<SyncState> {
  try {
    // Get agent details
    const { data: agent } = await supabase
      .from('agents')
      .select('*, organization:organization_id(*)')
      .eq('id', agentId)
      .single();

    if (!agent) {
      return {
        supabaseSuccess: false,
        elevenLabsSuccess: false,
        supabaseError: new Error('Agent not found')
      };
    }

    // Agent config is now part of the agent data
    const agentConfig = agent?.config;

    if (agent && agentConfig) {
      const orgId = agent.organization_id;
      // Use type assertion to handle the database schema mismatch
      const conversationConfig = (agentConfig as any).config || {};

      // Create a new ElevenLabs agent
      return await createElevenLabsAgent(
        supabase,
        agentId,
        agent.name,
        orgId,
        conversationConfig
      );
    }

    return {
      supabaseSuccess: false,
      elevenLabsSuccess: false,
      supabaseError: new Error('Agent or agent config not found')
    };
  } catch (error) {
    return {
      supabaseSuccess: false,
      elevenLabsSuccess: false,
      supabaseError: error instanceof Error ? error : new Error(String(error))
    };
  }
}

/**
 * Gets all agents for an organization from Supabase
 */
export async function getOrganizationAgents(
  supabase: SupabaseClient,
  organizationId: string
): Promise<any[]> {
  try {
    // First get all agents
    const { data: agents, error } = await supabase
      .from('agents')
      .select('*')
      .eq('organization_id', organizationId);

    if (error || !agents || agents.length === 0) {
      console.error('Error fetching organization agents:', error);
      return [];
    }

    // Agents now have their config embedded, no need to fetch separately
    return agents || [];
  } catch (error) {
    console.error('Error in getOrganizationAgents:', error);
    return [];
  }
}

/**
 * Gets all ElevenLabs agents for an organization by name pattern
 */
export async function getElevenLabsAgentsForOrg(
  organizationId: string
): Promise<any[]> {
  try {
    // Search for agents with the organization ID in their name
    const namePattern = `[SB-${organizationId}`;
    const allAgents = await elevenlabsQueries.findAgentsByNamePattern(namePattern);

    // Double-check that each agent actually contains the organization ID
    // This is an extra safety measure to ensure we don't process agents from other organizations
    const filteredAgents = (allAgents || []).filter(agent => {
      // Check if the agent name contains the exact organization ID pattern
      const orgPattern = `[SB-${organizationId}`;
      return agent.name && agent.name.includes(orgPattern);
    });

    return filteredAgents;
  } catch (error) {
    console.error('Error fetching ElevenLabs agents for org:', error);
    return [];
  }
}

/**
 * Maps Supabase agents to ElevenLabs agents
 */
export function mapAgents(
  supabaseAgents: any[],
  elevenLabsAgents: any[],
  organizationId: string
): AgentMapping[] {
  const mappings: AgentMapping[] = [];

  // Create a map of ElevenLabs agents by agent_id
  const elevenLabsAgentMap = new Map();

  for (const elevenLabsAgent of elevenLabsAgents) {
    elevenLabsAgentMap.set(elevenLabsAgent.agent_id, elevenLabsAgent);
  }

  // Create mappings for each Supabase agent
  for (const supabaseAgent of supabaseAgents) {
    // Get the ElevenLabs agent ID from the agent's external_id
    const elevenLabsAgentId = supabaseAgent.external_id;

    if (elevenLabsAgentId && supabaseAgent.type === 'elevenlabs') {
      // Check if this ElevenLabs agent exists
      const elevenLabsAgent = elevenLabsAgentMap.get(elevenLabsAgentId);

      if (elevenLabsAgent) {
        mappings.push({
          supabaseAgentId: supabaseAgent.id,
          elevenLabsAgentId: elevenLabsAgentId,
          name: supabaseAgent.name,
          organizationId
        });
      }
    }
  }

  return mappings;
}

/**
 * Synchronizes all agents for an organization
 */
export async function syncOrganizationAgents(
  supabase: SupabaseClient,
  organizationId: string
): Promise<{
  created: number;
  updated: number;
  deleted: number;
  errors: number;
}> {
  const result = {
    created: 0,
    updated: 0,
    deleted: 0,
    errors: 0
  };

  try {
    // Get all agents from Supabase
    const supabaseAgents = await getOrganizationAgents(supabase, organizationId);

    // Get all agents from ElevenLabs
    const elevenLabsAgents = await getElevenLabsAgentsForOrg(organizationId);

    // Create a map of ElevenLabs agents by agent_id
    const elevenLabsAgentMap = new Map();
    const processedElevenLabsAgentIds = new Set();

    for (const elevenLabsAgent of elevenLabsAgents) {
      elevenLabsAgentMap.set(elevenLabsAgent.agent_id, elevenLabsAgent);
    }

    // Filter out agents without configs (all agents should have configs now)
    const agentsWithConfigs = supabaseAgents.filter(agent => {
      const hasConfig = agent.config && typeof agent.config === 'object';

      if (!hasConfig) {
        console.debug(`Skipping agent ${agent.id} - no config found`);
      }

      return hasConfig;
    });

    // Process each Supabase agent that has a config
    for (const supabaseAgent of agentsWithConfigs) {
      try {
        // Get the conversation config from the agent
        const conversationConfig = (supabaseAgent.config as any) || {};

        // Get the ElevenLabs agent ID from the agent's external_id
        let elevenLabsAgentId = supabaseAgent.external_id;

        const elevenLabsAgent = elevenLabsAgentId ? elevenLabsAgentMap.get(elevenLabsAgentId) : undefined;

        // Debug logging
        console.log(`Processing agent ${supabaseAgent.id} (${supabaseAgent.name})`);
        console.log(`  - Agent type: ${supabaseAgent.type}`);
        console.log(`  - ElevenLabs agent ID: ${elevenLabsAgentId}`);
        console.log(`  - ElevenLabs agent found: ${!!elevenLabsAgent}`);

        // Check if the agent exists in ElevenLabs
        if (elevenLabsAgent) {
          // Mark this ElevenLabs agent as processed
          processedElevenLabsAgentIds.add(elevenLabsAgent.agent_id);

          // Agent exists in both systems, no update needed
          console.log(`Agent ${supabaseAgent.id} is already synchronized with ElevenLabs agent ${elevenLabsAgent.agent_id}`);
        } else if (elevenLabsAgentId) {
          // We have an ElevenLabs agent ID but the agent doesn't exist in ElevenLabs
          // This means the agent was deleted externally, so we need to clean up the database
          console.log(`ElevenLabs agent ${elevenLabsAgentId} for Supabase agent ${supabaseAgent.id} no longer exists, cleaning up database`);

          // Remove the invalid external_id from the database
          await supabase
            .from('agents')
            .update({ external_id: null })
            .eq('id', supabaseAgent.id);

          // Now create a new ElevenLabs agent if this is an elevenlabs type agent
          if (supabaseAgent.type === 'elevenlabs') {
            const formattedName = `${supabaseAgent.name || `Agent ${supabaseAgent.id}`} [SB-${organizationId}]`;

            try {
              // Create the agent in ElevenLabs
              const newElevenLabsAgent = await elevenlabsQueries.createAgent(
                formattedName,
                conversationConfig
              );

              // Update the agent with the new ElevenLabs agent ID
              await supabase
                .from('agents')
                .update({ external_id: newElevenLabsAgent.agent_id })
                .eq('id', supabaseAgent.id);

              // Mark this ElevenLabs agent as processed
              processedElevenLabsAgentIds.add(newElevenLabsAgent.agent_id);

              result.created++;
            } catch (error) {
              console.error(`Error creating ElevenLabs agent for ${supabaseAgent.id}:`, error);
              result.errors++;
            }
          }
        } else {
          // No external_id exists, create a new ElevenLabs agent if this is an elevenlabs type agent
          if (supabaseAgent.type === 'elevenlabs') {
            console.log(`No ElevenLabs external_id found for agent ${supabaseAgent.id}, creating new ElevenLabs agent`);

            const formattedName = `${supabaseAgent.name || `Agent ${supabaseAgent.id}`} [SB-${organizationId}]`;

            try {
              // Create the agent in ElevenLabs
              const newElevenLabsAgent = await elevenlabsQueries.createAgent(
                formattedName,
                conversationConfig
              );

              // Update the agent with the new ElevenLabs agent ID
              await supabase
                .from('agents')
                .update({ external_id: newElevenLabsAgent.agent_id })
                .eq('id', supabaseAgent.id);

              // Mark this ElevenLabs agent as processed
              processedElevenLabsAgentIds.add(newElevenLabsAgent.agent_id);

              result.created++;
            } catch (error) {
              console.error(`Error creating ElevenLabs agent for ${supabaseAgent.id}:`, error);
              result.errors++;
            }
          } else {
            console.log(`Agent ${supabaseAgent.id} is not an ElevenLabs agent, skipping`);
          }
        }
      } catch (error) {
        console.error(`Error processing agent ${supabaseAgent.id}:`, error);
        result.errors++;
      }
    }

    // Handle orphaned ElevenLabs agents (those that exist in ElevenLabs but not in Supabase)
    // This is optional and can be disabled if you don't want to delete orphaned agents
    console.log(`Checking for orphaned ElevenLabs agents. Total ElevenLabs agents: ${elevenLabsAgents.length}, Processed: ${processedElevenLabsAgentIds.size}`);

    // Temporarily disable orphaned agent deletion to debug the issue
    const ENABLE_ORPHANED_AGENT_DELETION = false;

    if (ENABLE_ORPHANED_AGENT_DELETION) {
      for (const elevenLabsAgent of elevenLabsAgents) {
        if (!processedElevenLabsAgentIds.has(elevenLabsAgent.agent_id)) {
          console.log(`Found unprocessed ElevenLabs agent: ${elevenLabsAgent.agent_id} (${elevenLabsAgent.name})`);

          try {
            // Triple-check that this agent belongs to the current organization
            // This is an extra safety measure to ensure we don't delete agents from other organizations
            const orgPattern = `[SB-${organizationId}`;
            if (!elevenLabsAgent.name || !elevenLabsAgent.name.includes(orgPattern)) {
              console.warn(`Skipping deletion of agent ${elevenLabsAgent.agent_id} as it doesn't match the organization pattern`);
              continue;
            }

            console.log(`Deleting orphaned ElevenLabs agent ${elevenLabsAgent.agent_id} (${elevenLabsAgent.name})`);

            // Delete the orphaned ElevenLabs agent
            await elevenlabsQueries.deleteAgent(elevenLabsAgent.agent_id);
            result.deleted++;
          } catch (error) {
            console.error(`Error deleting orphaned ElevenLabs agent ${elevenLabsAgent.agent_id}:`, error);
            result.errors++;
          }
        } else {
          console.log(`ElevenLabs agent ${elevenLabsAgent.agent_id} is properly mapped`);
        }
      }
    } else {
      console.log('Orphaned agent deletion is disabled for debugging');
    }

    return result;
  } catch (error) {
    console.error('Error in syncOrganizationAgents:', error);
    result.errors++;
    return result;
  }
}
