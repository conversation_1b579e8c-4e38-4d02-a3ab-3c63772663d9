import { createClient } from '@/utils/supabase/server';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Building2, Users, CreditCard, Activity } from 'lucide-react';
import Link from 'next/link';

async function getAdminStats() {
  const supabase = await createClient();
  
  // Get tenant count
  const { count: tenantCount } = await supabase
    .from('tenants')
    .select('*', { count: 'exact', head: true });
  
  // Get user count
  const { count: userCount } = await supabase
    .from('users')
    .select('*', { count: 'exact', head: true });
  
  // Get organization count
  const { count: orgCount } = await supabase
    .from('organizations')
    .select('*', { count: 'exact', head: true });
  
  // Get active conversations in the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const { count: conversationCount } = await supabase
    .from('conversations')
    .select('*', { count: 'exact', head: true })
    .gte('start_time', thirtyDaysAgo.toISOString());
  
  return {
    tenantCount: tenantCount || 0,
    userCount: userCount || 0,
    orgCount: orgCount || 0,
    conversationCount: conversationCount || 0
  };
}

export default async function AdminDashboard() {
  const stats = await getAdminStats();
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.tenantCount}</div>
            <p className="text-xs text-muted-foreground mt-1">
              White-label instances
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.userCount}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Registered users
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Organizations</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.orgCount}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Active organizations
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.conversationCount}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Conversations (30 days)
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common administrative tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/admin/tenants" 
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Manage tenants
                </Link>
              </li>
              <li>
                <Link 
                  href="/admin/users" 
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Manage users
                </Link>
              </li>
              <li>
                <Link 
                  href="/admin/settings" 
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  System settings
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current system health and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Database</span>
                <span className="text-green-600 dark:text-green-400">Online</span>
              </div>
              <div className="flex justify-between">
                <span>API Services</span>
                <span className="text-green-600 dark:text-green-400">Online</span>
              </div>
              <div className="flex justify-between">
                <span>Storage</span>
                <span className="text-green-600 dark:text-green-400">Online</span>
              </div>
              <div className="flex justify-between">
                <span>Authentication</span>
                <span className="text-green-600 dark:text-green-400">Online</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
