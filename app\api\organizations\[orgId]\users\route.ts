import { NextResponse } from 'next/server'
import { getOrganizationUsers } from '@/utils/supabase/queries'
import { createClient } from '@/utils/supabase/server'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    // Initialize Supabase client
    const supabase = await createClient()
    const {orgId} = await params;

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Verify organization access
    const { data: membership } = await supabase
      .from('organization_memberships')
      .select('organization_id, role')
      .eq('user_id', user.id)
      .eq('organization_id', orgId)
      .single()

    if (!membership) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')

    // Fetch users using the existing query
    const { data, count, error, hasMore } = await getOrganizationUsers(
      supabase,
      orgId,
      search,
      page,
      pageSize
    )

    if (error) {
      return new NextResponse('Internal Server Error', { status: 500 })
    }

    // Return success response
    return NextResponse.json({
      data,
      count,
      hasMore,
      error: null
    })

  } catch (error) {
    console.error('Error in users route:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    const { email, role } = await request.json()
    const { orgId } = await params;

    // Initialize Supabase client
    const supabase = await createClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Verify organization access and admin role
    const { data: membership } = await supabase
      .from('organization_memberships')
      .select('organization_id, role')
      .eq('user_id', user.id)
      .eq('organization_id', orgId)
      .single()

    if (!membership || membership.role !== 'admin') {
      return new NextResponse('Forbidden: Admin role required', { status: 403 })
    }

    // Check if the user already exists in the system
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single()

    let userId;

    if (existingUser) {
      // User exists, use their ID
      userId = existingUser.id;
    } else {
      // // User doesn't exist, create an invitation
      // // In a real implementation, you would send an email invitation
      // // For now, we'll just create a placeholder user
      // const { data: newUser, error: createError } = await supabase
      //   .from('users')
      //   .insert([
      //     { 
      //       email,
      //       full_name: email.split('@')[0], // Use part of email as name
      //       avatar_url: null,
      //       invited: true
      //     }
      //   ])
      //   .select()
      //   .single()

      // if (createError) {
      //   console.error('Error creating user:', createError)
      //   return new NextResponse('Failed to create user', { status: 500 })
      // }

      // userId = newUser.id;
    }

    // Add user to organization
    const { data, error } = await supabase
      .from('organization_memberships')
      .insert([
        {
          organization_id: orgId,
          user_id: userId,
          role: role || 'member' // Default to member if no role specified
        }
      ])
      .select()
      .single()

    if (error) {
      console.error('Error adding user to organization:', error)
      return new NextResponse('Failed to add user to organization', { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        ...data,
        email
      }
    })
  } catch (error) {
    console.error('Error in users route:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
