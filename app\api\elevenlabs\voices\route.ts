import { NextResponse } from 'next/server';
import { ElevenLabsClient } from 'elevenlabs';

export async function GET() {
  try {
    // Create ElevenLabs client using API key from environment variables
    const client = new ElevenLabsClient({
      apiKey: process.env.ELEVENLABS_API_KEY,
    });

    // Fetch voices from ElevenLabs API
    const voicesResponse = await client.voices.getAll();
    
    // Return the voices as JSON
    return NextResponse.json({ 
      voices: voicesResponse.voices,
      success: true 
    });
  } catch (error) {
    console.error('Error fetching ElevenLabs voices:', error);
    
    // Return error response
    return NextResponse.json(
      { 
        error: 'Failed to fetch voices from ElevenLabs',
        success: false 
      },
      { status: 500 }
    );
  }
}
