import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET /api/tenants/[id]/users
 * Get all users for a tenant
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });

    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    // Calculate pagination
    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;

    // Fetch users for this tenant
    let query = supabase
      .from('tenant_users')
      .select(`
        user:user_id (
          id,
          email,
          full_name,
          avatar_url,
          created_at
        ),
        is_primary,
        organizations:user_id(
          organization_memberships(
            organization:organization_id(
              id,
              name
            )
          )
        )
      `, { count: 'exact' })
      .eq('tenant_id', id);

    // Add search filter if provided
    if (search) {
      query = query.or(`user.email.ilike.%${search}%,user.full_name.ilike.%${search}%`);
    }

    // Add pagination
    query = query.range(start, end);

    // Execute query
    const { data, count, error } = await query;

    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    // Process the data to flatten the structure
    const processedData = data?.map(item => {
      // Extract user data
      const user = item.user;

      // Process organizations
      const organizations = [];
      if (item.organizations && item.organizations.organization_memberships) {
        for (const membership of item.organizations.organization_memberships) {
          if (membership.organization) {
            organizations.push({
              id: membership.organization.id,
              name: membership.organization.name
            });
          }
        }
      }

      return {
        ...user,
        isPrimary: item.is_primary,
        organizations
      };
    });

    // Calculate if there are more pages
    const hasMore = count ? start + pageSize < count : false;

    return NextResponse.json({
      data: processedData,
      count: count || 0,
      hasMore,
      page
    });
  } catch (error) {
    console.error('Error in tenant users route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tenants/[id]/users
 * Assign a user to a tenant
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });

    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Check if user exists
    const { data: userExists, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError || !userExists) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user is already in this tenant
    const { data: existingTenantUser } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('user_id', userId)
      .eq('tenant_id', id)
      .single();

    if (existingTenantUser) {
      return NextResponse.json({
        success: true,
        message: 'User is already in this tenant'
      });
    }

    // Check if this is the user's first tenant (to set as primary)
    const { data: userTenants } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('user_id', userId);

    const isPrimary = !userTenants || userTenants.length === 0;

    // Add user to tenant and create organization
    const { data: orgId, error: assignError } = await supabase
      .rpc('add_user_to_tenant', {
        user_id: userId,
        tenant_id: id,
        is_primary: isPrimary
      });

    if (assignError) {
      console.error('Error assigning user to tenant:', assignError);
      return NextResponse.json(
        { error: 'Failed to assign user to tenant' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        userId,
        tenantId: id,
        organizationId: orgId
      }
    });
  } catch (error) {
    console.error('Error in tenant users route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
