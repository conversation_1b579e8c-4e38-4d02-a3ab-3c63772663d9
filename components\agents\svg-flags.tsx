'use client'

import { cn } from "@/utils/cn"
import "./flags.css"

// Define the language data with ISO codes and SVG flags
export const languagesWithSvgFlags = [
  { code: 'en', name: 'English', region: 'United Kingdom' },
  { code: 'es', name: 'Spanish', region: 'Spain' },
  { code: 'fr', name: 'French', region: 'France' },
  { code: 'de', name: 'German', region: 'Germany' },
  { code: 'it', name: 'Italian', region: 'Italy' },
  { code: 'pt', name: 'Portuguese', region: 'Portugal' },
  { code: 'ru', name: 'Russian', region: 'Russia' },
  { code: 'zh', name: 'Chinese', region: 'China' },
  { code: 'ja', name: 'Japanese', region: 'Japan' },
  { code: 'ko', name: 'Korean', region: 'South Korea' },
  { code: 'ar', name: 'Arabic', region: 'Saudi Arabia' },
  { code: 'hi', name: 'Hindi', region: 'India' },
  { code: 'bn', name: 'Bengali', region: 'Bangladesh' },
  { code: 'nl', name: 'Dutch', region: 'Netherlands' },
  { code: 'tr', name: 'Turkish', region: 'Turkey' },
  { code: 'pl', name: 'Polish', region: 'Poland' },
  { code: 'sv', name: 'Swedish', region: 'Sweden' },
  { code: 'da', name: 'Danish', region: 'Denmark' },
  { code: 'fi', name: 'Finnish', region: 'Finland' },
  { code: 'no', name: 'Norwegian', region: 'Norway' },
  { code: 'cs', name: 'Czech', region: 'Czech Republic' },
  { code: 'hu', name: 'Hungarian', region: 'Hungary' },
  { code: 'el', name: 'Greek', region: 'Greece' },
  { code: 'he', name: 'Hebrew', region: 'Israel' },
  { code: 'th', name: 'Thai', region: 'Thailand' },
  { code: 'vi', name: 'Vietnamese', region: 'Vietnam' },
  { code: 'id', name: 'Indonesian', region: 'Indonesia' },
  { code: 'ms', name: 'Malay', region: 'Malaysia' },
  { code: 'fil', name: 'Filipino', region: 'Philippines' },
  { code: 'uk', name: 'Ukrainian', region: 'Ukraine' },
  { code: 'ro', name: 'Romanian', region: 'Romania' },
  { code: 'bg', name: 'Bulgarian', region: 'Bulgaria' },
  { code: 'hr', name: 'Croatian', region: 'Croatia' },
  { code: 'sr', name: 'Serbian', region: 'Serbia' },
  { code: 'sk', name: 'Slovak', region: 'Slovakia' },
  { code: 'sl', name: 'Slovenian', region: 'Slovenia' },
  { code: 'et', name: 'Estonian', region: 'Estonia' },
  { code: 'lv', name: 'Latvian', region: 'Latvia' },
  { code: 'lt', name: 'Lithuanian', region: 'Lithuania' },
  { code: 'fa', name: 'Persian', region: 'Iran' },
  { code: 'ur', name: 'Urdu', region: 'Pakistan' },
  { code: 'ta', name: 'Tamil', region: 'India' },
  { code: 'te', name: 'Telugu', region: 'India' },
  { code: 'ml', name: 'Malayalam', region: 'India' },
  { code: 'kn', name: 'Kannada', region: 'India' },
  { code: 'mr', name: 'Marathi', region: 'India' },
  { code: 'gu', name: 'Gujarati', region: 'India' },
  { code: 'pa', name: 'Punjabi', region: 'India' },
  { code: 'am', name: 'Amharic', region: 'Ethiopia' },
  { code: 'sw', name: 'Swahili', region: 'Kenya' },
]

export type LanguageCodeSvg = typeof languagesWithSvgFlags[number]['code']

interface SvgFlagProps {
  countryCode: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function SvgFlag({ countryCode, size = 'md', className }: SvgFlagProps) {
  const sizeClasses = {
    sm: 'h-5 w-5',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  // Convert country code to lowercase for URL
  const code = countryCode.toLowerCase()

  return (
    <div className={cn("overflow-hidden flex-shrink-0 bg-muted flex items-center justify-center", sizeClasses[size], className)}>
      <img
        src={`https://flagcdn.com/w80/${code}.png`}
        srcSet={`https://flagcdn.com/w160/${code}.png 2x`}
        width="100%"
        height="100%"
        alt={`Flag of ${countryCode}`}
        className="object-cover"
        loading="eager"
        onError={(e) => {
          // If image fails to load, show the first letter of the country code
          e.currentTarget.style.display = 'none';
          e.currentTarget.parentElement?.setAttribute('data-content', countryCode.charAt(0).toUpperCase());
        }}
      />
    </div>
  )
}

// Helper function to get country code from language code
export function getCountryCodeFromLanguage(languageCode: string): string {
  // Map language codes to country codes for flag display
  const languageToCountryMap: Record<string, string> = {
    en: 'gb', // English -> United Kingdom
    es: 'es', // Spanish -> Spain
    fr: 'fr', // French -> France
    de: 'de', // German -> Germany
    it: 'it', // Italian -> Italy
    pt: 'pt', // Portuguese -> Portugal
    ru: 'ru', // Russian -> Russia
    zh: 'cn', // Chinese -> China
    ja: 'jp', // Japanese -> Japan
    ko: 'kr', // Korean -> South Korea
    ar: 'sa', // Arabic -> Saudi Arabia
    hi: 'in', // Hindi -> India
    bn: 'bd', // Bengali -> Bangladesh
    nl: 'nl', // Dutch -> Netherlands
    tr: 'tr', // Turkish -> Turkey
    pl: 'pl', // Polish -> Poland
    sv: 'se', // Swedish -> Sweden
    da: 'dk', // Danish -> Denmark
    fi: 'fi', // Finnish -> Finland
    no: 'no', // Norwegian -> Norway
    cs: 'cz', // Czech -> Czech Republic
    hu: 'hu', // Hungarian -> Hungary
    el: 'gr', // Greek -> Greece
    he: 'il', // Hebrew -> Israel
    th: 'th', // Thai -> Thailand
    vi: 'vn', // Vietnamese -> Vietnam
    id: 'id', // Indonesian -> Indonesia
    ms: 'my', // Malay -> Malaysia
    fil: 'ph', // Filipino -> Philippines
    uk: 'ua', // Ukrainian -> Ukraine
    ro: 'ro', // Romanian -> Romania
    bg: 'bg', // Bulgarian -> Bulgaria
    hr: 'hr', // Croatian -> Croatia
    sr: 'rs', // Serbian -> Serbia
    sk: 'sk', // Slovak -> Slovakia
    sl: 'si', // Slovenian -> Slovenia
    et: 'ee', // Estonian -> Estonia
    lv: 'lv', // Latvian -> Latvia
    lt: 'lt', // Lithuanian -> Lithuania
    fa: 'ir', // Persian -> Iran
    ur: 'pk', // Urdu -> Pakistan
    ta: 'in', // Tamil -> India
    te: 'in', // Telugu -> India
    ml: 'in', // Malayalam -> India
    kn: 'in', // Kannada -> India
    mr: 'in', // Marathi -> India
    gu: 'in', // Gujarati -> India
    pa: 'in', // Punjabi -> India
    am: 'et', // Amharic -> Ethiopia
    sw: 'ke', // Swahili -> Kenya
  }

  return languageToCountryMap[languageCode] || languageCode
}

// Component for language flag with SVG
export function LanguageFlagSvg({
  languageCode,
  size = 'md',
  className
}: {
  languageCode: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const countryCode = getCountryCodeFromLanguage(languageCode)

  return (
    <SvgFlag
      countryCode={countryCode}
      size={size}
      className={className}
    />
  )
}
