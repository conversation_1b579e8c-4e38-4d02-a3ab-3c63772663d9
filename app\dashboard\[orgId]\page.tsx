import { AppSidebar } from "@/components/app-sidebar"
import { ChartAreaInteractive } from "@/components/chart-area-interactive"
import { DataTable } from "@/components/data-table"
import { SectionCards } from "@/components/section-cards"
import { SiteHeader } from "@/components/site-header"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { createClient } from "@/utils/supabase/server"
import { getDashboardStats } from "@/utils/supabase/queries"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface PageProps {
  params: Promise<{
    orgId: string,
  }>
}

export default async function Page({ params }: PageProps) {
  const { orgId } = await params;
  const supabase = await createClient();
  const stats = await getDashboardStats(supabase, orgId);

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
            <SectionCards
              totalCalls={stats.totalCalls}
              avgDurationSeconds={stats.avgDurationSeconds}
              avgCostCents={stats.avgCostCents}
              totalCostCents={stats.totalCostCents}
            />
          </Suspense>
          <div className="px-4 lg:px-6">
            <Suspense fallback={<Skeleton className="h-[350px] w-full" />}>
              <ChartAreaInteractive callsData={stats.callsByDate} />
            </Suspense>
          </div>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <DataTable data={stats.recentConversations} />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
