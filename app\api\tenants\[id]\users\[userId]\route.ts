import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * DELETE /api/tenants/[id]/users/[userId]
 * Remove a user from a tenant
 */
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string; userId: string }> }
) {
  try {
    const { id, userId } = await params;

    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });

    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Check if user exists and belongs to this tenant
    const { data: tenantUser, error: tenantUserError } = await supabase
      .from('tenant_users')
      .select('id, is_primary')
      .eq('user_id', userId)
      .eq('tenant_id', id)
      .single();

    if (tenantUserError || !tenantUser) {
      return NextResponse.json(
        { error: 'User not found or not assigned to this tenant' },
        { status: 404 }
      );
    }

    // Get organizations for this user in this tenant
    const { data: organizations } = await supabase
      .from('organizations')
      .select('id')
      .eq('tenant_id', id)
      .in('id', 
        await supabase
          .from('organization_memberships')
          .select('organization_id')
          .eq('user_id', userId)
          .then(result => (result.data?.map(item => item.organization_id).filter(Boolean) as string[]) || [])
      );

    // Begin transaction to remove user from tenant
    // 1. Remove user from tenant_users table
    // 2. Remove user from all organizations in this tenant
    const { error: deleteError } = await supabase
      .from('tenant_users')
      .delete()
      .eq('user_id', userId)
      .eq('tenant_id', id);

    if (deleteError) {
      console.error('Error removing user from tenant:', deleteError);
      return NextResponse.json(
        { error: 'Failed to remove user from tenant' },
        { status: 500 }
      );
    }

    // If this was the user's primary tenant, assign a new primary tenant if available
    if (tenantUser.is_primary) {
      const { data: otherTenants } = await supabase
        .from('tenant_users')
        .select('id, tenant_id')
        .eq('user_id', userId)
        .limit(1);

      if (otherTenants && otherTenants.length > 0) {
        // Set the first other tenant as primary
        await supabase
          .from('tenant_users')
          .update({ is_primary: true })
          .eq('id', otherTenants[0].id);
      }
    }

    // Remove user from all organizations in this tenant
    if (organizations && organizations.length > 0) {
      const orgIds = organizations.map(org => org.id);

      const { error: membershipError } = await supabase
        .from('organization_memberships')
        .delete()
        .eq('user_id', userId)
        .in('organization_id', orgIds);

      if (membershipError) {
        console.error('Error removing user from organizations:', membershipError);
        // Continue anyway, as the main operation (removing from tenant_users) succeeded
      }
    }

    return NextResponse.json({
      success: true,
      message: 'User removed from tenant successfully'
    });
  } catch (error) {
    console.error('Error in tenant users route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}


