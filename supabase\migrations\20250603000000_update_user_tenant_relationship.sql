-- Add tenant_id to users table
ALTER TABLE "public"."users" 
ADD COLUMN IF NOT EXISTS "tenant_id" "uuid" REFERENCES "public"."tenants"("id") ON DELETE SET NULL;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS "idx_users_tenant_id" ON "public"."users" ("tenant_id");

-- Update the handle_new_user function to work with tenants
CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
declare
  new_org_id uuid := gen_random_uuid();
  tenant_id_var uuid;
begin
  -- Try to get tenant_id from the request cookie
  -- This will be set by the middleware when a user signs up through a tenant subdomain
  BEGIN
    tenant_id_var := nullif(current_setting('request.cookie.tenant_id', true), '')::uuid;
  EXCEPTION
    WHEN OTHERS THEN
      tenant_id_var := NULL;
  END;
  
  -- Insert user with tenant_id
  insert into public.users (id, email, full_name, avatar_url, tenant_id)
  values (new.id, new.email, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url', tenant_id_var);

  -- Create org (only if tenant_id is set - otherwise this will be handled when they first access a tenant)
  if tenant_id_var is not null then
    insert into public.organizations (id, name, tenant_id)
    values (new_org_id, 'My Workspace', tenant_id_var);

    -- Add as admin
    insert into public.organization_memberships (organization_id, user_id, role)
    values (new_org_id, new.id, 'admin');

    -- Add wallet
    insert into public.credit_wallets (organization_id, balance_cents)
    values (new_org_id, 0);
  else
    -- For users not created through a tenant, create an organization without tenant_id
    insert into public.organizations (id, name)
    values (new_org_id, 'My Workspace');

    -- Add as admin
    insert into public.organization_memberships (organization_id, user_id, role)
    values (new_org_id, new.id, 'admin');

    -- Add wallet
    insert into public.credit_wallets (organization_id, balance_cents)
    values (new_org_id, 0);
  end if;

  return new;
end;
$$;

-- Create function to assign user to tenant and create organization
CREATE OR REPLACE FUNCTION "public"."assign_user_to_tenant"("user_id" uuid, "tenant_id" uuid) RETURNS uuid
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  new_org_id uuid := gen_random_uuid();
BEGIN
  -- Update user's tenant_id
  UPDATE public.users
  SET tenant_id = assign_user_to_tenant.tenant_id
  WHERE id = assign_user_to_tenant.user_id;
  
  -- Check if user already has an organization in this tenant
  IF NOT EXISTS (
    SELECT 1
    FROM public.organizations o
    JOIN public.organization_memberships om ON o.id = om.organization_id
    WHERE om.user_id = assign_user_to_tenant.user_id
    AND o.tenant_id = assign_user_to_tenant.tenant_id
  ) THEN
    -- Create a new organization for this user in the tenant
    INSERT INTO public.organizations (id, name, tenant_id)
    VALUES (new_org_id, 'My Workspace', assign_user_to_tenant.tenant_id);
    
    -- Add user as admin
    INSERT INTO public.organization_memberships (organization_id, user_id, role)
    VALUES (new_org_id, assign_user_to_tenant.user_id, 'admin');
    
    -- Add wallet
    INSERT INTO public.credit_wallets (organization_id, balance_cents)
    VALUES (new_org_id, 0);
    
    RETURN new_org_id;
  ELSE
    -- Return the existing organization ID
    RETURN (
      SELECT o.id
      FROM public.organizations o
      JOIN public.organization_memberships om ON o.id = om.organization_id
      WHERE om.user_id = assign_user_to_tenant.user_id
      AND o.tenant_id = assign_user_to_tenant.tenant_id
      LIMIT 1
    );
  END IF;
END;
$$;
