# Testing Scripts

This directory contains scripts for testing various aspects of the application.

## Orphaned Organizations Test

The `test_orphaned_orgs.js` script tests the automatic deletion of orphaned organizations (organizations with no members).

### Prerequisites

Before running the script, make sure you have:

1. Node.js installed
2. Required npm packages installed:
   ```bash
   npm install @supabase/supabase-js dotenv
   ```
3. A `.env.local` file in the root directory with the following variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

### Running the Test

To run the test:

```bash
node scripts/test_orphaned_orgs.js
```

### What the Test Does

The script:

1. Creates a test user
2. Creates a test organization
3. Creates a membership for the test user in the organization
4. Creates related data (credit wallet, team, agent)
5. Deletes the membership, which should trigger the deletion of the orphaned organization
6. Verifies that the organization has been deleted
7. Cleans up the test user

### Expected Output

If the test is successful, you should see output similar to:

```
Testing orphaned organization deletion...
Creating test user...
Test user created with ID: [user-id]
Creating test organization...
Test organization created with ID: [org-id]
Creating membership...
Membership created with ID: [membership-id]
Creating credit wallet...
Creating team...
Team created with ID: [team-id]
Creating agent...
Verifying organization exists...
Organization exists, now deleting the membership...
Membership deleted, waiting for trigger to execute...
Verifying organization has been deleted...
SUCCESS: Organization was deleted as expected!
Cleaning up test user...
Test user deleted successfully
Test completed!
```

If the test fails, it will show an error message indicating what went wrong.

### Troubleshooting

If you encounter issues:

1. Make sure your Supabase URL and service role key are correct
2. Check that the database migration for orphaned organization deletion has been applied
3. Verify that your Supabase instance is running and accessible
4. Check the Supabase logs for any errors related to the trigger or function execution

## Applying the Trigger Directly

If you're having issues with the migration process, you can apply the trigger and function directly using the SQL script:

1. Navigate to the Supabase dashboard for your project
2. Go to the SQL Editor
3. Copy the contents of `apply_orphaned_orgs_trigger.sql` and paste it into the SQL Editor
4. Run the SQL script

This will create or replace the necessary functions and triggers without going through the migration process.
