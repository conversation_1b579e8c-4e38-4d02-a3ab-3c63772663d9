import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET /api/tenants/[id]
 * Get a specific tenant by ID
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });
    
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Fetch tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching tenant:', error);
      return NextResponse.json(
        { error: 'Failed to fetch tenant' },
        { status: 500 }
      );
    }

    if (!tenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: tenant
    });
  } catch (error) {
    console.error('Error in tenant route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/tenants/[id]
 * Update a tenant
 */
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const updates = await request.json();
    
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a tenant admin or system admin
    const { data: isTenantAdmin } = await supabase
      .rpc('is_tenant_admin', { tenant_id: id });
    
    const { data: isAdmin } = await supabase
      .rpc('is_admin');

    if (!isTenantAdmin && !isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Prevent changing slug or domain if they already exist
    if (updates.slug) {
      const { data: existingSlug } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', updates.slug)
        .neq('id', id)
        .single();

      if (existingSlug) {
        return NextResponse.json(
          { error: 'Tenant slug already exists' },
          { status: 400 }
        );
      }
    }

    if (updates.domain) {
      const { data: existingDomain } = await supabase
        .from('tenants')
        .select('id')
        .eq('domain', updates.domain)
        .neq('id', id)
        .single();

      if (existingDomain) {
        return NextResponse.json(
          { error: 'Domain already exists' },
          { status: 400 }
        );
      }
    }

    // Add updated_at timestamp
    updates.updated_at = new Date().toISOString();

    // Update tenant
    const { data, error } = await supabase
      .from('tenants')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating tenant:', error);
      return NextResponse.json(
        { error: 'Failed to update tenant' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Error in tenant route:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
