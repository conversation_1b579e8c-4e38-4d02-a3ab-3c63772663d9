"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatTime, formatDuration } from '@/utils/format'
import { 
  Search, 
  MoreHorizontal, 
  ChevronDown, 
  ChevronUp, 
  Clock,
  MessageSquare,
  CheckCircle2,
  XCircle
} from 'lucide-react'

// Types
interface Conversation {
  id: string
  timestamp: string
  agent_name: string
  agent_id: string
  duration_seconds: number
  message_count: number
  status: 'successful' | 'failed'
  cost_cents?: number
}

type SortField = 'timestamp' | 'agent_name' | 'duration_seconds' | 'message_count' | 'status';
type SortDirection = 'asc' | 'desc';

interface ConversationsTableProps {
  conversations: Conversation[]
  orgId: string
}

export function ConversationsTable({ conversations, orgId }: ConversationsTableProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [sortField, setSortField] = useState<SortField>('timestamp')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [agentFilter, setAgentFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Get unique agents for filter
  const uniqueAgents = Array.from(new Set(conversations.map(conv => conv.agent_name)))
    .map(name => ({
      id: conversations.find(conv => conv.agent_name === name)?.agent_id || '',
      name
    }));

  // Handle sort click
  const handleSortClick = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Navigate to conversation details
  const navigateToConversation = (conversationId: string) => {
    router.push(`/dashboard/${orgId}/conversations/${conversationId}`)
  }

  // Filter and sort conversations
  const filteredConversations = conversations
    .filter(conv => {
      // Apply search filter
      const matchesSearch = conv.agent_name.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Apply agent filter
      const matchesAgent = agentFilter === 'all' || conv.agent_id === agentFilter;
      
      // Apply status filter
      const matchesStatus = statusFilter === 'all' || conv.status === statusFilter;
      
      return matchesSearch && matchesAgent && matchesStatus;
    })
    .sort((a, b) => {
      if (sortField === 'timestamp') {
        const dateA = new Date(a.timestamp).getTime()
        const dateB = new Date(b.timestamp).getTime()
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA
      } else if (sortField === 'agent_name') {
        const comparison = a.agent_name.localeCompare(b.agent_name)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'duration_seconds') {
        return sortDirection === 'asc' 
          ? a.duration_seconds - b.duration_seconds 
          : b.duration_seconds - a.duration_seconds
      } else if (sortField === 'message_count') {
        return sortDirection === 'asc' 
          ? a.message_count - b.message_count 
          : b.message_count - a.message_count
      } else if (sortField === 'status') {
        const statusA = a.status === 'successful' ? 1 : 0
        const statusB = b.status === 'successful' ? 1 : 0
        return sortDirection === 'asc' ? statusA - statusB : statusB - statusA
      }
      return 0
    })

  return (
    <>
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-4 gap-4">
        <div className="relative w-full md:max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search conversations..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex flex-col md:flex-row gap-2 w-full md:w-auto">
          <Select
            value={agentFilter}
            onValueChange={setAgentFilter}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="All agents" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All agents</SelectItem>
              {uniqueAgents.map((agent) => (
                <SelectItem key={agent.id} value={agent.id}>
                  {agent.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="All results" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All results</SelectItem>
              <SelectItem value="successful">Successful</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('timestamp')}
              >
                <div className="flex items-center">
                  Date & Time
                  {sortField === 'timestamp' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('agent_name')}
              >
                <div className="flex items-center">
                  Agent
                  {sortField === 'agent_name' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('message_count')}
              >
                <div className="flex items-center">
                  Messages
                  {sortField === 'message_count' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('duration_seconds')}
              >
                <div className="flex items-center">
                  Duration
                  {sortField === 'duration_seconds' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('status')}
              >
                <div className="flex items-center">
                  Status
                  {sortField === 'status' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center">
                  Actions
                </div>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredConversations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  {searchQuery || agentFilter !== 'all' || statusFilter !== 'all' 
                    ? 'No conversations found matching your search criteria.' 
                    : 'No conversations found.'}
                </TableCell>
              </TableRow>
            ) : (
              filteredConversations.map((conversation) => (
                <TableRow 
                  key={conversation.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => navigateToConversation(conversation.id)}
                >
                  <TableCell>
                    <div className="font-medium">{formatDate(conversation.timestamp)}</div>
                    <div className="text-sm text-muted-foreground">{formatTime(conversation.timestamp)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{conversation.agent_name}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-1 text-muted-foreground" />
                      {conversation.message_count}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                      {formatDuration(conversation.duration_seconds)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {conversation.status === 'successful' ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                        <CheckCircle2 className="h-3 w-3" />
                        Successful
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
                        <XCircle className="h-3 w-3" />
                        Failed
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          navigateToConversation(conversation.id);
                        }}>
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Delete conversation logic would go here
                          }}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </>
  )
}
