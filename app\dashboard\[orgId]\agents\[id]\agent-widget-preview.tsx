'use client'

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { WidgetPreview } from '@/components/agents/widget-preview';
import { Clipboard } from 'lucide-react';
import { EmbedCodeModal } from '@/components/agents/embed-code-modal';

interface AgentWidgetPreviewProps {
  agentId: string;
  orgId: string;
  agentConfig: any; // Agent config from the agents table
}

export const AgentWidgetPreview = ({ agentId, orgId, agentConfig }: AgentWidgetPreviewProps) => {
  const [showEmbedModal, setShowEmbedModal] = useState(false);

  // Note: agentConfig is available here if needed for future functionality
  // Currently, the WidgetPreview component gets its settings from the Zustand store
  console.log('Agent config loaded on server:', agentConfig);

  return (
    <div className="h-full flex flex-col">
      <div className='flex justify-between mb-4'>
        <div>
          <h1 className="text-2xl font-bold">Widget Preview</h1>
          <p className="text-muted-foreground">Test your agent as you make changes</p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant="outline"
            onClick={() => setShowEmbedModal(true)}
          >
            <Clipboard className="h-4 w-4 mr-2" />
            Embed Code
          </Button>
        </div>
      </div>

      {/* Widget preview takes up the full height */}
      <div className="h-[calc(100vh-150px)] flex-1">
        <WidgetPreview agentId={agentId} orgId={orgId} />
      </div>

      {/* Embed code modal */}
      <EmbedCodeModal
        open={showEmbedModal}
        onOpenChange={setShowEmbedModal}
        agentId={agentId}
      />
    </div>
  );
};
