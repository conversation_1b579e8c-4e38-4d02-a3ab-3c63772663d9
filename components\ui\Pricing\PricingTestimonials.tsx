'use client';

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Quote } from "lucide-react";

const testimonials = [
  {
    quote: "BotCom AI has transformed our customer service. Our AI agents handle routine inquiries 24/7, freeing our team to focus on complex issues.",
    author: "<PERSON>",
    title: "Customer Service Director",
    company: "TechCorp",
    avatar: "/avatars/sarah.jpg"
  },
  {
    quote: "The analytics dashboard gives us incredible insights into customer interactions. We've been able to optimize our agents and improve satisfaction rates by 35%.",
    author: "<PERSON>",
    title: "Operations Manager",
    company: "Global Retail",
    avatar: "/avatars/michael.jpg"
  },
  {
    quote: "Setting up our first AI agent was surprisingly easy. The team management features help us organize our agents by department and track budgets effectively.",
    author: "<PERSON>",
    title: "IT Director",
    company: "FinanceHub",
    avatar: "/avatars/emily.jpg"
  }
];

export function PricingTestimonials() {
  return (
    <div className="w-full">
      <h2 className="text-2xl font-bold text-center mb-8">What Our Customers Say</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {testimonials.map((testimonial, index) => (
          <Card key={index} className="bg-zinc-50 dark:bg-zinc-800/50 border-0">
            <CardContent className="p-6">
              <Quote className="h-8 w-8 text-primary/20 mb-4" />
              <p className="text-zinc-700 dark:text-zinc-300 mb-6 italic">
                "{testimonial.quote}"
              </p>
              <div className="flex items-center">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarFallback>{testimonial.author.charAt(0)}</AvatarFallback>
                  <AvatarImage src={testimonial.avatar} alt={testimonial.author} />
                </Avatar>
                <div>
                  <p className="font-medium">{testimonial.author}</p>
                  <p className="text-sm text-zinc-500 dark:text-zinc-400">
                    {testimonial.title}, {testimonial.company}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
