'use client'

import { useState, useMemo, useEffect, useTransition } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { AddUserModal } from './add-user-modal'
import {
  Search,
  Plus,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  Filter,
  Mail,
  Calendar,
  Shield
} from 'lucide-react'
import { formatDate } from '@/utils/format'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface User {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: string
  member_since: string
}

interface UsersTableProps {
  users: User[]
  orgId: string
}

type SortField = 'full_name' | 'email' | 'role' | 'member_since';
type SortDirection = 'asc' | 'desc';
type RoleFilter = 'admin' | 'member' | null;

export function UsersTable({ users, orgId }: UsersTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isPending, startTransition] = useTransition()
  
  // Initialize state from URL parameters
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '')
  const [showAddModal, setShowAddModal] = useState(false)
  const [sortField, setSortField] = useState<SortField>(
    (searchParams.get('sort') as SortField) || 'member_since'
  )
  const [sortDirection, setSortDirection] = useState<SortDirection>(
    (searchParams.get('direction') as SortDirection) || 'desc'
  )
  const [roleFilter, setRoleFilter] = useState<RoleFilter>(
    searchParams.get('role') as RoleFilter
  )

  // Update URL with current filter state
  useEffect(() => {
    startTransition(() => {
      const params = new URLSearchParams()
      
      // Add all filters to URL
      if (searchQuery) params.set('search', searchQuery)
      if (sortField !== 'member_since') params.set('sort', sortField)
      if (sortDirection !== 'desc') params.set('direction', sortDirection)
      if (roleFilter) params.set('role', roleFilter)
      
      // Update URL without refreshing the page
      const url = `${window.location.pathname}?${params.toString()}`
      router.replace(url, { scroll: false })
    })
  }, [searchQuery, sortField, sortDirection, roleFilter, router])

  // Handle sort click
  const handleSortClick = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new field and default to ascending
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Filter and sort users
  const filteredAndSortedUsers = useMemo(() => {
    // First filter by search query
    let result = users.filter(user =>
      (user.full_name?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Then filter by role if set
    if (roleFilter) {
      result = result.filter(user => user.role === roleFilter)
    }

    // Then sort
    return result.sort((a, b) => {
      if (sortField === 'full_name') {
        const nameA = a.full_name || ''
        const nameB = b.full_name || ''
        const comparison = nameA.localeCompare(nameB)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'email') {
        const comparison = a.email.localeCompare(b.email)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'role') {
        const comparison = a.role.localeCompare(b.role)
        return sortDirection === 'asc' ? comparison : -comparison
      } else if (sortField === 'member_since') {
        const dateA = new Date(a.member_since || 0).getTime()
        const dateB = new Date(b.member_since || 0).getTime()
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA
      }
      return 0
    })
  }, [users, searchQuery, sortField, sortDirection, roleFilter])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Users</h1>
          <p className="text-muted-foreground">Manage users and permissions</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Invite user
          </Button>
        </div>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search users..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value)
          }}
        />
      </div>

      <div className="flex items-center gap-2 mb-4">
        {roleFilter && (
          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
            <span>Role: {roleFilter}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1 p-0"
              onClick={() => setRoleFilter(null)}
            >
              <span className="sr-only">Remove filter</span>
              ×
            </Button>
          </Badge>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              <DropdownMenuLabel>Role</DropdownMenuLabel>
              <DropdownMenuCheckboxItem
                checked={roleFilter === 'admin'}
                onCheckedChange={(checked) => {
                  setRoleFilter(checked ? 'admin' : null)
                }}
              >
                Admin
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={roleFilter === 'member'}
                onCheckedChange={(checked) => {
                  setRoleFilter(checked ? 'member' : null)
                }}
              >
                Member
              </DropdownMenuCheckboxItem>
            </DropdownMenuGroup>

            {roleFilter && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => {
                  // Clear all filters
                  setRoleFilter(null)
                  
                  // Clear URL params immediately
                  startTransition(() => {
                    router.replace(window.location.pathname, { scroll: false })
                  })
                }}>
                  Clear all filters
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">User</TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('email')}
              >
                <div className="flex items-center">
                  Email
                  {sortField === 'email' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('role')}
              >
                <div className="flex items-center">
                  Role
                  {sortField === 'role' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSortClick('member_since')}
              >
                <div className="flex items-center">
                  Member Since
                  {sortField === 'member_since' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar_url || undefined} alt={user.full_name || ''} />
                        <AvatarFallback>
                          {user.full_name ? user.full_name.substring(0, 2).toUpperCase() : user.email.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <Link
                        href={`/dashboard/${orgId}/users/${user.id}`}
                        className="font-medium hover:underline"
                      >
                        {user.full_name || user.email.split('@')[0]}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      {user.email}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.role === 'admin' ? 'default' : 'outline'}>
                      {user.role === 'admin' ? (
                        <div className="flex items-center gap-1">
                          <Shield className="h-3 w-3 mr-1" />
                          Admin
                        </div>
                      ) : (
                        'Member'
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {formatDate(user.member_since)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => router.push(`/dashboard/${orgId}/users/${user.id}`)}
                        >
                          Edit user
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => {
                            // In a real app, you would show a confirmation dialog
                            alert('This would remove the user from the organization')
                          }}
                        >
                          Remove from organization
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <AddUserModal
        open={showAddModal}
        onOpenChange={setShowAddModal}
        orgId={orgId}
        onSuccess={() => {
          // Refresh the page to show the new user
          router.refresh()
        }}
      />
    </div>
  )
}
