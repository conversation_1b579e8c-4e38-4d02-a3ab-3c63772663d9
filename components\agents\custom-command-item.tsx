'use client'

import React from 'react'
import { CommandItem } from '@/components/ui/command'
import { cn } from '@/utils/cn'

interface CustomCommandItemProps extends React.ComponentPropsWithoutRef<typeof CommandItem> {
  onItemClick?: () => void
}

export const CustomCommandItem = React.forwardRef<
  React.ElementRef<typeof CommandItem>,
  CustomCommandItemProps
>(({ className, onItemClick, onSelect, children, ...props }, ref) => {
  // Handle both click and onSelect
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (onItemClick) {
      onItemClick()
    }
    
    // If onSelect is provided, manually trigger it
    if (typeof onSelect === 'function' && props.value) {
      onSelect(props.value)
    }
  }
  
  return (
    <CommandItem
      ref={ref}
      className={cn('cursor-pointer', className)}
      onSelect={onSelect}
      onClick={handleClick}
      {...props}
    >
      {children}
    </CommandItem>
  )
})

CustomCommandItem.displayName = 'CustomCommandItem'
