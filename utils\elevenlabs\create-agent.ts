/**
 * Utility function to create an ElevenLabs agent
 */
export async function createElevenLabsAgent(params: {
  agentId: string;
  name: string;
  voiceId: string;
  orgId: string;
  description?: string;
}) {
  const { agentId, name, voiceId, orgId, description } = params;

  try {
    // Call the API to create an ElevenLabs agent
    const response = await fetch(`/api/elevenlabs/create-agent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        agentId,
        name,
        voiceId,
        description: description || `Agent created by BotCom dashboard for organization ${orgId}`
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error creating ElevenLabs agent:', errorData);
      throw new Error(`Failed to create ElevenLabs agent: ${errorData.error || 'Unknown error'}`);
    }

    const data = await response.json();
    console.log('ElevenLabs agent created successfully:', data.agent_id);
    return data;
  } catch (error) {
    console.error('Error creating ElevenLabs agent:', error);
    throw error;
  }
}
