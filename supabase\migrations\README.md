# Database Migrations

This directory contains Supabase database migrations for the BotCom SaaS application.

## Migration Files

- `000_initial_schema.sql` - Initial database schema
- `20250502000000_add_user_trigger.sql` - Adds the trigger to create organizations when users sign up

## Applying Migrations

### Option 1: Using the Script

```bash
node scripts/apply-migrations.js
```

### Option 2: Apply Migrations Manually

```bash
supabase migration up
```

### Option 3: Reset the Database (Development Only)

This will completely reset your database, which means all data will be lost. Only use this in development environments.

```bash
supabase db reset
```

## Creating New Migrations

When you need to make changes to the database schema, create a new migration file:

```bash
supabase migration new your_migration_name
```

This will create a new file with a timestamp prefix in the migrations directory.

## Database Schema

The current schema includes the following tables:

- `organizations` - Stores organization information
- `agent_teams` - Stores teams that agents can belong to
- `agents` - Stores agent information
- `agent_configs` - Stores provider-specific configurations for agents
- `agent_budget_limits` - Stores budget limits for agents
- `conversations` - Stores conversation information
- `conversation_messages` - Stores individual messages in conversations

## User Registration Flow

When a new user registers:

1. The `on_auth_user_created` trigger fires
2. The `handle_new_user` function is called
3. A new record is created in the `users` table
4. A new organization is created for the user
5. The user is added as an admin to the organization
6. A credit wallet is created for the organization

## Voice Settings

Voice settings for agents are stored in two places:
1. The `tts_config` JSON field in the `agent_configs` table
2. The `custom_metadata` JSON field in the `agent_configs` table

When updating voice settings, make sure to update both fields as needed.
