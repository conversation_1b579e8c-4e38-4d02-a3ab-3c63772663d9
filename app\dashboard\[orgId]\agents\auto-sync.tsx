'use client'

import { useEffect, useRef } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { useToast } from '@/components/ui/Toasts/use-toast'

interface AutoSyncProps {
  organizationId: string
}

export function AutoSync({ organizationId }: AutoSyncProps) {
  const { toast } = useToast()

  // Track if we've already performed a sync
  const hasSyncedRef = useRef(false);

  // Track the last sync time
  const lastSyncTimeRef = useRef(0);

  // Minimum time between syncs (10 minutes)
  const MIN_SYNC_INTERVAL = 10 * 60 * 1000;

  // Query to check sync status
  const { data: syncStatus, isLoading, error } = useQuery({
    queryKey: ['sync-status-org', organizationId],
    queryFn: async () => {
      const response = await fetch(`/api/sync/auto?organizationId=${organizationId}`)
      if (!response.ok) {
        throw new Error('Failed to check sync status')
      }
      return await response.json()
    },
    // Check less frequently to avoid API rate limits
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!organizationId
  })

  // Mutation to trigger sync
  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      // Update the last sync time
      lastSyncTimeRef.current = Date.now();

      const response = await fetch('/api/sync/auto', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ organizationId })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to sync')
      }

      return await response.json()
    },
    onSuccess: (data) => {
      const { result } = data

      // Mark that we've performed a sync
      hasSyncedRef.current = true;

      if (result.created > 0 || result.updated > 0 || result.deleted > 0) {
        toast({
          title: 'Synchronization Complete',
          description: `Created: ${result.created}, Updated: ${result.updated}, Deleted: ${result.deleted}`,
        })
      }
    },
    onError: (error) => {
      console.error('Sync error:', error)
      // Don't show error toast for background sync to avoid annoying users
    }
  })

  // Automatically trigger sync if needed
  useEffect(() => {
    // Only proceed if we have sync status and we're not already syncing
    if (!syncStatus || isPending) return;

    // Check if sync is needed
    if (syncStatus.needsSync) {
      // Check if enough time has passed since the last sync
      const now = Date.now();
      const timeSinceLastSync = now - lastSyncTimeRef.current;

      // Only sync if:
      // 1. We haven't synced yet during this session, or
      // 2. Enough time has passed since the last sync
      if (!hasSyncedRef.current || timeSinceLastSync > MIN_SYNC_INTERVAL) {
        console.log('Triggering sync because:',
          !hasSyncedRef.current ? 'First sync of the session' : 'Time interval elapsed');
        mutate();
      } else {
        console.log('Skipping sync - too soon since last sync');
      }
    }
  }, [syncStatus, mutate, isPending])

  // This component doesn't render anything
  return null
}
