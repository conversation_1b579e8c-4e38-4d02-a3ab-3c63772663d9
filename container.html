<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Host Website</title>
    <style>
      /* Control panel styles */
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .control-panel {
        max-width: 800px;
        margin: 0 auto 450px auto; /* Increased bottom margin to make room for widget */
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
      }

      h2 {
        margin-top: 0;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
      }

      h3 {
        margin-top: 20px;
        color: #555;
      }

      .panel-section {
        margin-bottom: 20px;
      }

      .control-group {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }

      label {
        width: 150px;
        display: inline-block;
        font-weight: 500;
      }

      input[type="text"], select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        width: 250px;
        font-size: 14px;
      }

      input[type="checkbox"] {
        transform: scale(1.2);
      }

      textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: monospace;
        font-size: 14px;
        resize: vertical;
      }

      .button-group {
        margin-top: 20px;
        display: flex;
        gap: 10px;
      }

      button {
        padding: 10px 15px;
        background-color: #4a90e2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: background-color 0.2s;
      }

      button:hover {
        background-color: #3a80d2;
      }

      #update-css-btn {
        background-color: #5cb85c;
      }

      #update-css-btn:hover {
        background-color: #4cae4c;
      }

      #copy-embed-btn {
        background-color: #9c27b0;
      }

      #copy-embed-btn:hover {
        background-color: #7b1fa2;
      }

      #embed-code {
        font-family: monospace;
        font-size: 13px;
        background-color: #f8f8f8;
      }

      .copy-message {
        color: #4cae4c;
        font-weight: 500;
        margin-top: 8px;
        display: none;
        animation: fadeOut 2s forwards;
        animation-delay: 2s;
      }

      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }

      .embed-code-container {
        position: relative;
      }

      .copy-again-button {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 6px 12px;
        background-color: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        color: #333;
        transition: all 0.2s;
      }

      .copy-again-button:hover {
        background-color: #e8e8e8;
      }

      .copy-again-button svg {
        width: 14px;
        height: 14px;
      }

      /* Initial widget styles - these will be overridden by the dynamic CSS */
      #widget-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        width: 300px; /* Give it some width */
        height: 400px; /* Give it some height */
      }

      ::part(widget-container) {
        position: relative; /* Changed from fixed to relative */
        bottom: auto;
        right: auto;
      }

      ::part(widget) {
        border-radius: 0px;
      }

      ::part(start-button) {
        background-color: #2e62c9;
        font-family: "Inter",Sans-serif;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        border-radius: 8px;
        background-image: linear-gradient(to right,#25aae1,#40b8e4,#3078dd,#2b5eb6);
        background-size: 300% 100%;
      }

      ::part(start-button):hover {
        background-position: 100% 0;
        transition: all .4s ease-in-out;
      }

      ::part(end-button) {
        background-image: linear-gradient(to right,#e12525,#e44040,#dd3030,#b62b2b);
        background-size: 300% 100%;
        font-family: "Inter",Sans-serif;
        color: #fff;
        border-radius: 8px;
      }

      ::part(end-button):hover {
        background-position: 100% 0;
        transition: all .4s ease-in-out;
      }

      ::part(avatar-container) {
        color: white;
        background-color: #2e62c9;
      }
    </style>


  </head>
  <body>
    <div class="control-panel">
      <h2>Widget Control Panel</h2>

      <div class="panel-section">
        <h3>Widget Properties</h3>
        <div class="control-group">
          <label for="widget-type">Widget Type:</label>
          <select id="widget-type">
            <option value="compact">Compact</option>
            <option value="standalone">Standalone</option>
            <option value="webspeech">Web Speech</option>
          </select>
        </div>

        <div class="control-group">
          <label for="hide-icon">Hide Icon:</label>
          <input type="checkbox" id="hide-icon">
        </div>
      </div>

      <div class="panel-section">
        <h3>Text Customization</h3>
        <div class="control-group">
          <label for="text-need-help">Need Help Text:</label>
          <input type="text" id="text-need-help" value="Want to chat?">
        </div>

        <div class="control-group">
          <label for="text-start-call">Start Button Text:</label>
          <input type="text" id="text-start-call" value="Start conversation">
        </div>

        <div class="control-group">
          <label for="text-end-call">End Button Text:</label>
          <input type="text" id="text-end-call" value="Finish chat">
        </div>
      </div>

      <div class="panel-section">
        <h3>CSS Editor</h3>
        <textarea id="css-editor" rows="10" placeholder="Enter custom CSS here...">/* Custom styles for widget parts */
#widget-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  width: 300px;
  height: 400px;
}

::part(widget-container) {
  position: relative;
  bottom: auto;
  right: auto;
}

::part(widget) {
  border-radius: 0px;
}

::part(start-button) {
  background-color: #2e62c9;
  font-family: "Inter",Sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  border-radius: 8px;
  background-image: linear-gradient(to right,#25aae1,#40b8e4,#3078dd,#2b5eb6);
  background-size: 300% 100%;
}

::part(end-button) {
  background-image: linear-gradient(to right,#e12525,#e44040,#dd3030,#b62b2b);
  background-size: 300% 100%;
  font-family: "Inter",Sans-serif;
  color: #fff;
  border-radius: 8px;
}

::part(avatar-container) {
  color: white;
  background-color: #2e62c9;
}</textarea>
      </div>

      <div class="button-group">
        <button id="update-widget-btn" onclick="updateWidgetFromControls()">Update Widget</button>
        <button id="update-css-btn" onclick="updateCSS()">Update CSS</button>
        <button id="copy-embed-btn" onclick="copyEmbedCode()">Copy Embed Code</button>
      </div>

      <div class="panel-section" id="embed-code-section" style="display: none;">
        <h3>Embed Code</h3>
        <div class="embed-code-container">
          <textarea id="embed-code" rows="15" readonly></textarea>
          <button id="copy-again-btn" onclick="copyEmbedCodeAgain()" class="copy-again-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            Copy
          </button>
        </div>
        <div class="copy-message" id="copy-message">Copied to clipboard!</div>
      </div>
    </div>

    <div id="widget-container" style="display: block; position: fixed; bottom: 20px; right: 20px; z-index: 1000; width: 300px; height: 400px;"></div>

    <!-- Include the widget script -->
    <!-- <script src="http://localhost:3000/widget.iife.js"></script> -->
    <!-- <script src="http://localhost:3000/widget/widget.iife.js"></script> -->
    <!-- <script src="https://botcom--smart-call-widget.netlify.app/widget.iife.js"></script> -->
    <script src="./dist/widget.iife.js"></script>

    <!-- Initialize the widget -->
    <script>
      // Store the widget instance in a global variable
      const widget = ConversationWidget({
        agentId: '4bYTqcv2o7NvB5vfZWZC',
        position: 'bottom-right',
        containerId: 'widget-container',
        type: 'compact', // Changed from webspeech to standalone
        customParts: {
          container: 'widget-container',
          startButton: 'start-button',
          endButton: 'end-button',
          avatarContainer: 'avatar-container',
          statusText: 'status-text',
          hideIcon: false,
          // Custom icon
          customIcon: `<svg aria-hidden="true" fill="currentColor" stroke="currentColor" class="e-font-icon-svg e-fab-rocketchat" viewBox="0 0 576 512" xmlns="http://www.w3.org/2000/svg"><path d="M284.046,224.8a34.114,34.114,0,1,0,34.317,34.113A34.217,34.217,0,0,0,284.046,224.8Zm-110.45,0a34.114,34.114,0,1,0,34.317,34.113A34.217,34.217,0,0,0,173.6,224.8Zm220.923,0a34.114,34.114,0,1,0,34.317,34.113A34.215,34.215,0,0,0,394.519,224.8Zm153.807-55.319c-15.535-24.172-37.31-45.57-64.681-63.618-52.886-34.817-122.374-54-195.666-54a405.975,405.975,0,0,0-72.032,6.357,238.524,238.524,0,0,0-49.51-36.588C99.684-11.7,40.859.711,11.135,11.421A14.291,14.291,0,0,0,5.58,34.782C26.542,56.458,61.222,99.3,52.7,138.252c-33.142,33.9-51.112,74.776-51.112,117.337,0,43.372,17.97,84.248,51.112,118.148,8.526,38.956-26.154,81.816-47.116,103.491a14.284,14.284,0,0,0,5.555,23.34c29.724,10.709,88.549,23.147,155.324-10.2a238.679,238.679,0,0,0,49.51-36.589A405.972,405.972,0,0,0,288,460.14c73.313,0,142.8-19.159,195.667-53.975,27.371-18.049,49.145-39.426,64.679-63.619,17.309-26.923,26.07-55.916,26.07-86.125C574.394,225.4,565.634,196.43,548.326,169.485ZM284.987,409.9a345.65,345.65,0,0,1-89.446-11.5l-20.129,19.393a184.366,184.366,0,0,1-37.138,27.585,145.767,145.767,0,0,1-52.522,14.87c.983-1.771,1.881-3.563,2.842-5.356q30.258-55.68,16.325-100.078c-32.992-25.962-52.778-59.2-52.778-95.4,0-83.1,104.254-150.469,232.846-150.469s232.867,67.373,232.867,150.469C517.854,342.525,413.6,409.9,284.987,409.9Z"></path></svg>`,
          customText: {
            needHelp: 'Want to chat?',
            talkToInterrupt: 'Speak now to interrupt',
            listening: "I'm listening...",
            startCall: 'Start conversation',
            endCall: 'Finish chat',
            noMessages: 'Start your conversation',
            aiLabel: 'Assistant',
            userLabel: 'Me',
            chatHistory: 'Conversation History'
          }
        }
      });

      // Function to update widget with current control panel values
      function updateWidgetFromControls() {
        const type = document.getElementById('widget-type').value;
        const hideIcon = document.getElementById('hide-icon').checked;
        const needHelp = document.getElementById('text-need-help').value;
        const startCall = document.getElementById('text-start-call').value;
        const endCall = document.getElementById('text-end-call').value;

        widget.update({
          type: type,
          customParts: {
            hideIcon: hideIcon,
            customText: {
              needHelp: needHelp,
              startCall: startCall,
              endCall: endCall
            }
          }
        });
      }

      // Function to update CSS
      function updateCSS() {
        const cssEditor = document.getElementById('css-editor');
        const styleTag = document.getElementById('dynamic-styles');

        if (styleTag) {
          styleTag.textContent = cssEditor.value;
        } else {
          const newStyle = document.createElement('style');
          newStyle.id = 'dynamic-styles';
          newStyle.textContent = cssEditor.value;
          document.head.appendChild(newStyle);
        }
      }

      // Function to generate embed code based on current settings
      function generateEmbedCode() {
        // Get current widget settings
        const type = document.getElementById('widget-type').value;
        const hideIcon = document.getElementById('hide-icon').checked;
        const needHelp = document.getElementById('text-need-help').value;
        const startCall = document.getElementById('text-start-call').value;
        const endCall = document.getElementById('text-end-call').value;
        const css = document.getElementById('css-editor').value;

        // Get the custom icon from the current widget configuration
        const customIcon = `
        <svg aria-hidden="true" fill="currentColor" stroke="currentColor" class="e-font-icon-svg e-fab-rocketchat" viewBox="0 0 576 512" xmlns="http://www.w3.org/2000/svg"><path d="M284.046,224.8a34.114,34.114,0,1,0,34.317,34.113A34.217,34.217,0,0,0,284.046,224.8Zm-110.45,0a34.114,34.114,0,1,0,34.317,34.113A34.217,34.217,0,0,0,173.6,224.8Zm220.923,0a34.114,34.114,0,1,0,34.317,34.113A34.215,34.215,0,0,0,394.519,224.8Zm153.807-55.319c-15.535-24.172-37.31-45.57-64.681-63.618-52.886-34.817-122.374-54-195.666-54a405.975,405.975,0,0,0-72.032,6.357,238.524,238.524,0,0,0-49.51-36.588C99.684-11.7,40.859.711,11.135,11.421A14.291,14.291,0,0,0,5.58,34.782C26.542,56.458,61.222,99.3,52.7,138.252c-33.142,33.9-51.112,74.776-51.112,117.337,0,43.372,17.97,84.248,51.112,118.148,8.526,38.956-26.154,81.816-47.116,103.491a14.284,14.284,0,0,0,5.555,23.34c29.724,10.709,88.549,23.147,155.324-10.2a238.679,238.679,0,0,0,49.51-36.589A405.972,405.972,0,0,0,288,460.14c73.313,0,142.8-19.159,195.667-53.975,27.371-18.049,49.145-39.426,64.679-63.619,17.309-26.923,26.07-55.916,26.07-86.125C574.394,225.4,565.634,196.43,548.326,169.485ZM284.987,409.9a345.65,345.65,0,0,1-89.446-11.5l-20.129,19.393a184.366,184.366,0,0,1-37.138,27.585,145.767,145.767,0,0,1-52.522,14.87c.983-1.771,1.881-3.563,2.842-5.356q30.258-55.68,16.325-100.078c-32.992-25.962-52.778-59.2-52.778-95.4,0-83.1,104.254-150.469,232.846-150.469s232.867,67.373,232.867,150.469C517.854,342.525,413.6,409.9,284.987,409.9Z"></path></svg>
        `;

        // Generate embed code with all current settings
        return `<!-- Widget Container -->
<div id="widget-container"></div>

<!-- Widget Styles -->
<`+`style>
${css}
</style>

<`+`script src="https://botcom--smart-call-widget.netlify.app/widget.iife.js"></>

<!-- Initialize Widget -->
<`+`script>
  const widget = ConversationWidget({
    agentId: '4bYTqcv2o7NvB5vfZWZC', // Replace with your agent ID
    containerId: 'widget-container',
    type: '${type}',
    customParts: {
      container: 'widget-container',
      startButton: 'start-button',
      endButton: 'end-button',
      avatarContainer: 'avatar-container',
      statusText: 'status-text',
      hideIcon: ${hideIcon},
      customIcon: ${JSON.stringify(customIcon)},
      customText: {
        needHelp: '${needHelp}',
        startCall: '${startCall}',
        endCall: '${endCall}',
        noMessages: 'Start your conversation',
        aiLabel: 'Assistant',
        userLabel: 'Me',
        chatHistory: 'Conversation History'
      }
    }
  });
</>
`;
      }

      // Function to generate and copy embed code
      function copyEmbedCode() {
        // Generate the embed code
        const embedCode = generateEmbedCode();

        // Show the embed code section
        const embedSection = document.getElementById('embed-code-section');
        embedSection.style.display = 'block';

        // Set the embed code in the textarea
        const embedTextarea = document.getElementById('embed-code');
        embedTextarea.value = embedCode;

        // Copy to clipboard using the modern Clipboard API
        copyToClipboard(embedCode);
      }

      // Function to copy text to clipboard using the modern Clipboard API
      function copyToClipboard(text) {
        // Check if the Clipboard API is available
        if (navigator.clipboard) {
          navigator.clipboard.writeText(text)
            .then(() => {
              // Show success message
              const copyMessage = document.getElementById('copy-message');
              copyMessage.textContent = 'Copied to clipboard!';
              copyMessage.style.display = 'block';

              // Hide the message after animation completes
              setTimeout(() => {
                copyMessage.style.display = 'none';
              }, 4000);
            })
            .catch(err => {
              console.error('Failed to copy: ', err);
              // Fallback to the old method if Clipboard API fails
              fallbackCopyToClipboard(text);
            });
        } else {
          // Fallback for browsers that don't support Clipboard API
          fallbackCopyToClipboard(text);
        }
      }

      // Fallback copy method for older browsers
      function fallbackCopyToClipboard(text) {
        const textarea = document.getElementById('embed-code');
        textarea.select();

        try {
          const successful = document.execCommand('copy');
          if (successful) {
            const copyMessage = document.getElementById('copy-message');
            copyMessage.textContent = 'Copied to clipboard!';
            copyMessage.style.display = 'block';

            setTimeout(() => {
              copyMessage.style.display = 'none';
            }, 4000);
          } else {
            throw new Error('Copy command was unsuccessful');
          }
        } catch (err) {
          console.error('Failed to copy: ', err);
          const copyMessage = document.getElementById('copy-message');
          copyMessage.textContent = 'Please select and copy the code manually (Ctrl+C)';
          copyMessage.style.color = '#e74c3c';
          copyMessage.style.display = 'block';

          setTimeout(() => {
            copyMessage.style.display = 'none';
            copyMessage.style.color = '#4cae4c'; // Reset color
          }, 6000);
        }
      }

      // Function to copy the embed code again after it's displayed
      function copyEmbedCodeAgain() {
        // Get the current embed code from the textarea
        const embedCode = generateEmbedCode();

        // Update the textarea with the latest code (in case settings changed)
        const embedTextarea = document.getElementById('embed-code');
        embedTextarea.value = embedCode;

        // Copy to clipboard
        copyToClipboard(embedCode);
      }

      // Initialize control panel with current widget settings
      document.addEventListener('DOMContentLoaded', function() {
        // Set the widget type dropdown
        document.getElementById('widget-type').value = 'compact';

        // Set the hide icon checkbox
        document.getElementById('hide-icon').checked = false;

        // Set the text fields
        document.getElementById('text-need-help').value = 'Want to chat?';
        document.getElementById('text-start-call').value = 'Start conversation';
        document.getElementById('text-end-call').value = 'Finish chat';

        // Apply the initial CSS
        updateCSS();

        // Make sure the widget container is visible
        document.getElementById('widget-container').style.display = 'block';
      });

      // Apply CSS immediately as well
      updateCSS();
    </script>
    <!-- <script>
      ConversationWidget({
        agentId: 'asst_l9zc14YU61btG6wrtEspzo06',
        position: 'bottom-right',
        containerId: 'widget-container',
        type: 'webspeech'
      });
    </script> -->
  </body>
</html>