Stack trace:
Frame         Function      Args
0007FFFF9EE0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8DE0) msys-2.0.dll+0x2118E
0007FFFF9EE0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9EE0  0002100469F2 (00021028DF99, 0007FFFF9D98, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9EE0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9EE0  00021006A545 (0007FFFF9EF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9EF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9843A0000 ntdll.dll
7FF982260000 KERNEL32.DLL
7FF981580000 KERNELBASE.dll
7FF982CE0000 USER32.dll
7FF981950000 win32u.dll
7FF9829F0000 GDI32.dll
7FF981DA0000 gdi32full.dll
7FF9820F0000 msvcp_win.dll
7FF981C50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF983400000 advapi32.dll
7FF9826E0000 msvcrt.dll
7FF982F30000 sechost.dll
7FF9825C0000 RPCRT4.dll
7FF980B70000 CRYPTBASE.DLL
7FF981BB0000 bcryptPrimitives.dll
7FF982490000 IMM32.DLL
