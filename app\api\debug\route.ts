import { NextResponse } from 'next/server';

/**
 * GET /api/debug
 * Debug endpoint to check environment variables and configuration
 * Only available in development mode
 */
export async function GET(request: Request) {
  // Only allow in development mode
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 403 }
    );
  }

  // Get environment variables related to domain configuration
  const envVars = {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_ROOT_DOMAIN: process.env.NEXT_PUBLIC_ROOT_DOMAIN,
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
    NEXT_PUBLIC_VERCEL_URL: process.env.NEXT_PUBLIC_VERCEL_URL,
    NEXT_PUBLIC_ENABLE_TENANTS: process.env.NEXT_PUBLIC_ENABLE_TENANTS,
  };

  // Get request information
  const url = new URL(request.url);
  const requestInfo = {
    url: request.url,
    hostname: url.hostname,
    pathname: url.pathname,
    protocol: url.protocol,
  };

  return NextResponse.json({
    envVars,
    requestInfo,
  });
}
