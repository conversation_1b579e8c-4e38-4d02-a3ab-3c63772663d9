-- Create admin role check function
CREATE OR REPLACE FUNCTION "public"."is_admin"() RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the current user's email is in the admin list
  -- You can customize this to use a dedicated admin table or other logic
  RETURN EXISTS (
    SELECT 1
    FROM public.users
    WHERE users.id = auth.uid()
    AND (
      -- Add your admin emails here
      users.email IN (
        '<EMAIL>',
        current_setting('request.jwt.claims', true)::json->>'email'
      )
      -- Alternatively, you could add an is_admin column to the users table
      -- OR users.is_admin = true
    )
  );
END;
$$;

-- Create RLS policy for tenant admins to manage tenant
CREATE POLICY "Tenant admins can manage tenant" ON "public"."tenants"
FOR UPDATE USING (
  "public"."is_tenant_admin"(id)
);

-- Create RLS policy for system admins to manage all tenants
CREATE POLICY "System admins can manage all tenants" ON "public"."tenants"
FOR ALL USING (
  "public"."is_admin"()
);

-- Create RLS policy for system admins to manage all organizations
CREATE POLICY "System admins can manage all organizations" ON "public"."organizations"
FOR ALL USING (
  "public"."is_admin"()
);
