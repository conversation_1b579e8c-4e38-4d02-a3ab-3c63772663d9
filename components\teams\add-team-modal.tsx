"use client"

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON>alog<PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useRouter } from 'next/navigation'

interface AddTeamModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  orgId: string
}

export function AddTeamModal({ open, onOpenChange, orgId }: AddTeamModalProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    budget: 5000, // Default budget of $5,000 (will be converted to cents on the server)
  })
  const [error, setError] = useState<string | null>(null)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    if (name === 'budget') {
      // Parse as number and ensure it's not negative
      const numValue = Math.max(0, parseFloat(value) || 0)
      setFormData(prev => ({ ...prev, [name]: numValue }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!formData.name.trim()) {
      setError('Team name is required')
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/organizations/${orgId}/teams`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          budget: formData.budget,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create team')
      }

      // Reset form and close modal
      setFormData({
        name: '',
        budget: 5000,
      })
      onOpenChange(false)

      // Refresh the teams list
      router.refresh()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] bg-white dark:bg-zinc-900 text-zinc-900 dark:text-zinc-50">
        <DialogHeader>
          <DialogTitle className="text-zinc-900 dark:text-zinc-50">Create New Team</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          {error && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="name" className="text-zinc-900 dark:text-zinc-50">Team Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter team name"
              className="text-zinc-900 dark:text-zinc-50"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="budget" className="text-zinc-900 dark:text-zinc-50">Budget (USD)</Label>
            <div className="relative">
              <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
              <Input
                id="budget"
                name="budget"
                type="number"
                min="0"
                step="100"
                value={formData.budget}
                onChange={handleChange}
                className="pl-7 text-zinc-900 dark:text-zinc-50"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Set the maximum budget for this team. This will be used to track spending.
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Team'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
