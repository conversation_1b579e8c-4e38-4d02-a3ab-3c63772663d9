/**
 * Client-side utilities for interacting with ElevenLabs
 */

/**
 * Generate a signed URL for an ElevenLabs agent
 * 
 * @param agentId The ID of the agent in the database
 * @param estimatedCost Optional estimated cost in cents (default: 10)
 * @returns A promise that resolves to the signed URL
 */
export async function generateSignedUrl(agentId: string, estimatedCost?: number): Promise<string> {
  try {
    const response = await fetch('/api/elevenlabs/generate-signed-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        agentId,
        ...(estimatedCost !== undefined && { estimatedCost })
      }),
    });
    
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || data.details || 'Failed to generate signed URL');
    }

    if (!data.url) {
      throw new Error('No URL returned from the server');
    }

    return data.url;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw error;
  }
}

/**
 * Start a call with an ElevenLabs agent
 * 
 * @param agentId The ID of the agent in the database
 * @param options Optional configuration options
 * @returns A promise that resolves when the call is started
 */
export async function startCall(
  agentId: string, 
  options?: { 
    estimatedCost?: number,
    onSignedUrlGenerated?: (url: string) => void,
    onCallStarted?: () => void,
    onError?: (error: Error) => void
  }
): Promise<void> {
  try {
    // Generate a signed URL
    const signedUrl = await generateSignedUrl(agentId, options?.estimatedCost);
    
    // Call the onSignedUrlGenerated callback if provided
    if (options?.onSignedUrlGenerated) {
      options.onSignedUrlGenerated(signedUrl);
    }
    
    // Here you would typically use the signed URL to establish a connection
    // with the ElevenLabs service. This could involve:
    // 1. Opening a WebSocket connection
    // 2. Initializing a client-side SDK
    // 3. Redirecting to a page that handles the call
    
    // For now, we'll just log that the call would start
    console.log('Call would start with signed URL:', signedUrl);
    
    // Call the onCallStarted callback if provided
    if (options?.onCallStarted) {
      options.onCallStarted();
    }
  } catch (error) {
    console.error('Error starting call:', error);
    
    // Call the onError callback if provided
    if (options?.onError && error instanceof Error) {
      options.onError(error);
    }
    
    throw error;
  }
}
