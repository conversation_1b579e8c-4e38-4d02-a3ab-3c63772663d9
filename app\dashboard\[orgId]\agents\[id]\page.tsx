
import { Suspense } from 'react'
import { cn } from "@/utils/cn"
import Link from 'next/link'
import { createClient } from '@/utils/supabase/server'
import { getAgentDetails } from '@/utils/supabase/queries'
import { AgentSettingsSidebar } from '@/components/agents/settings/agent-settings-sidebar'
import { AgentWidgetPreview } from './agent-widget-preview'

interface PageProps {
  params: Promise<{
    orgId: string,
    id: string
  }>
}

export default async function AgentPage({ params }: PageProps) {
  const { orgId, id } = await params;
  console.log('--', orgId, id);

  const supabase = await createClient();

  // Fetch agent details
  const agent = await getAgentDetails(supabase, id);

  // Agent config is now part of the agent data
  const agentConfig = agent?.config;

  return (
    <div className="@container/main grid grid-cols-12 h-full relative">
      {/* Left column: Agent settings sidebar */}
      <div className={cn(
        "border-r overflow-y-auto",
        "transition-[grid-column] duration-300 ease-in-out",
        "col-span-12 @xl/main:col-span-6 @2xl/main:col-span-5"
      )}>
        {/* Client-side settings form */}
        <AgentSettingsSidebar agentId={id} orgId={orgId} agentData={agent} />
      </div>

      {/* Right column: Widget preview */}
      <div className={cn(
        "overflow-y-auto",
        "transition-[grid-column] duration-300 ease-in-out",
        "hidden @xl/main:block @xl/main:col-span-6 @2xl/main:col-span-7"
      )}>
        <div className="p-6 h-full">
          <Suspense fallback={
            <div className="h-full flex items-center justify-center">
              Loading widget preview...
            </div>
          }>
            <AgentWidgetPreview agentId={id} orgId={orgId} agentConfig={agentConfig} />
          </Suspense>
        </div>
      </div>

      {/* Mobile view - only show a back button */}
      <div className="@xl/main:hidden p-4">
        <Link
          href={`/dashboard/${orgId}/agents`}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          ← Back to Agents
        </Link>
      </div>
    </div>
  )
}