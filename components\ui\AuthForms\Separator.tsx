interface SeparatorProps {
  text: string;
}

export default function Separator({ text }: SeparatorProps) {
  return (
    <div className="relative">
      <div className="relative flex items-center py-1">
        <div className="grow border-t border-zinc-200 dark:border-zinc-700"></div>
        <span className="mx-3 shrink text-sm text-zinc-500 dark:text-zinc-400">
          {text}
        </span>
        <div className="grow border-t border-zinc-200 dark:border-zinc-700"></div>
      </div>
    </div>
  );
}
